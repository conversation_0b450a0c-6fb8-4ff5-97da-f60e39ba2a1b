-- DISABLE UNNECESSARY REALTIME TABLES
-- This will stop the realtime.list_changes() performance issue

-- First, check what tables currently have realtime enabled
SELECT 
    schemaname,
    tablename,
    'ALTER PUBLICATION supabase_realtime DROP TABLE ' || schemaname || '.' || tablename || ';' as disable_command
FROM pg_publication_tables 
WHERE publication_name = 'supabase_realtime'
ORDER BY schemaname, tablename;

-- Disable realtime for tables that don't need it
-- (Based on the dashboard subscriptions we just removed)
-- Note: Some commands may fail if table isn't in publication - that's OK

DO $$
BEGIN
    -- Disable diary_entries (dashboard doesn't need real-time)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.diary_entries;
        RAISE NOTICE 'Disabled realtime for diary_entries';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'diary_entries was not in realtime publication';
    END;

    -- Disable subscriptions (changes infrequently)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.subscriptions;
        RAISE NOTICE 'Disabled realtime for subscriptions';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'subscriptions was not in realtime publication';
    END;

    -- Disable audio_posts (dashboard doesn't need real-time)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.audio_posts;
        RAISE NOTICE 'Disabled realtime for audio_posts';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'audio_posts was not in realtime publication';
    END;

    -- Disable audio_reactions (dashboard doesn't need real-time)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.audio_reactions;
        RAISE NOTICE 'Disabled realtime for audio_reactions';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'audio_reactions was not in realtime publication';
    END;

    -- Disable audio_loves (dashboard doesn't need real-time)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.audio_loves;
        RAISE NOTICE 'Disabled realtime for audio_loves';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'audio_loves was not in realtime publication';
    END;

    -- Disable audio_replies (dashboard doesn't need real-time)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.audio_replies;
        RAISE NOTICE 'Disabled realtime for audio_replies';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'audio_replies was not in realtime publication';
    END;

    -- Disable projects (changes infrequently)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.projects;
        RAISE NOTICE 'Disabled realtime for projects';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'projects was not in realtime publication';
    END;

    -- Disable users (rarely needs real-time updates)
    BEGIN
        ALTER PUBLICATION supabase_realtime DROP TABLE public.users;
        RAISE NOTICE 'Disabled realtime for users';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'users was not in realtime publication';
    END;
END $$;

-- Keep only essential tables for real-time:
-- - comments (for real-time chat)
-- - direct_messages (for messaging)
-- - payments (for immediate earnings updates)

-- Re-enable only the essential ones (in case they were disabled)
ALTER PUBLICATION supabase_realtime ADD TABLE public.comments;
ALTER PUBLICATION supabase_realtime ADD TABLE public.direct_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.payments;

-- Optional: Also keep reactions for real-time heart animations
ALTER PUBLICATION supabase_realtime ADD TABLE public.reactions;

-- Check what's enabled after cleanup
SELECT 
    schemaname,
    tablename
FROM pg_publication_tables 
WHERE publication_name = 'supabase_realtime'
ORDER BY schemaname, tablename;

-- Clean up any orphaned realtime subscriptions
DELETE FROM realtime.subscription 
WHERE entity NOT IN (
    SELECT format('%I.%I', schemaname, tablename)::regclass
    FROM pg_publication_tables 
    WHERE publication_name = 'supabase_realtime'
);

-- Vacuum the realtime tables to reclaim space
VACUUM realtime.subscription;
VACUUM realtime.messages;
