'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'

interface Correspondence {
  id: string
  type: 'moment' | 'letter' | 'note'
  title: string
  body: string
  data?: any
  read_at?: string
  created_at: string
  sender?: {
    id: string
    name: string
    avatar?: string
  }
}

interface CorrespondencePanelProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  onUnreadCountChange?: (count: number) => void
}



export function CorrespondencePanel({ isOpen, onClose, userId, onUnreadCountChange }: CorrespondencePanelProps) {
  const [activeTab, setActiveTab] = useState<'messages' | 'notifications'>('messages')
  const [messages, setMessages] = useState<Correspondence[]>([])
  const [notifications, setNotifications] = useState<Correspondence[]>([])
  const [loading, setLoading] = useState(true)

  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen && userId) {
      fetchCorrespondence()
    }
  }, [isOpen, userId, activeTab])

  // Real-time subscriptions for live updates
  useEffect(() => {
    if (!userId) return

    const channel = supabase
      .channel('correspondence')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'direct_messages',
        filter: `recipient_id=eq.${userId}`
      }, () => {
        if (isOpen) fetchCorrespondence()
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, () => {
        if (isOpen) fetchCorrespondence()
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [userId, isOpen])

  const fetchCorrespondence = async () => {
    setLoading(true)
    try {
      // Fetch notifications separately
      const { data: notificationsData } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      // Fetch direct messages separately
      const { data: messagesData } = await supabase
        .from('direct_messages')
        .select(`
          *,
          sender:users!direct_messages_sender_id_fkey(id, name, avatar, profile_picture_url)
        `)
        .eq('recipient_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      // Format notifications
      const formattedNotifications: Correspondence[] = (notificationsData || []).map(notif => ({
        id: notif.id,
        type: 'note' as const,
        title: notif.title,
        body: notif.body,
        data: notif.data,
        read_at: notif.read_at,
        created_at: notif.created_at,
        sender: notif.data?.sender_name ? {
          id: notif.data.sender_id,
          name: notif.data.sender_name,
        } : undefined
      }))

      // Format messages
      const formattedMessages: Correspondence[] = (messagesData || []).map(msg => ({
        id: msg.id,
        type: 'letter' as const,
        title: msg.subject || 'New Message',
        body: msg.body || 'Photo message',
        data: {
          sender_id: msg.sender_id,
          photo_url: msg.photo_url,
          message_type: msg.message_type
        },
        read_at: msg.read_at,
        created_at: msg.created_at,
        sender: {
          id: msg.sender.id,
          name: msg.sender.name,
          avatar: msg.sender.profile_picture_url || msg.sender.avatar
        }
      }))

      setNotifications(formattedNotifications)
      setMessages(formattedMessages)
    } catch (error) {
      console.error('Error fetching correspondence:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (correspondenceId: string, type: 'moment' | 'letter' | 'note') => {
    try {
      if (type === 'letter') {
        // Mark direct message as read
        await supabase
          .from('direct_messages')
          .update({ read_at: new Date().toISOString() })
          .eq('id', correspondenceId)

        // Update local messages state
        setMessages(prev =>
          prev.map(item =>
            item.id === correspondenceId
              ? { ...item, read_at: new Date().toISOString() }
              : item
          )
        )
      } else {
        // Mark notification as read
        await supabase
          .from('notifications')
          .update({ read_at: new Date().toISOString() })
          .eq('id', correspondenceId)

        // Update local notifications state
        setNotifications(prev =>
          prev.map(item =>
            item.id === correspondenceId
              ? { ...item, read_at: new Date().toISOString() }
              : item
          )
        )
      }

      // Notify parent component to update unread count
      if (onUnreadCountChange) {
        const allItems = [...messages, ...notifications]
        const newUnreadCount = allItems.filter(item =>
          !item.read_at && item.id !== correspondenceId
        ).length
        onUnreadCountChange(newUnreadCount)
      }
    } catch (error) {
      console.error('Error marking as read:', error)
    }
  }



  const currentItems = activeTab === 'messages' ? messages : notifications

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  const handleItemClick = (item: Correspondence) => {
    // Mark as read first
    if (!item.read_at) {
      markAsRead(item.id, item.type)
    }

    if (activeTab === 'messages' && item.type === 'letter' && item.sender) {
      // For messages, go to the messages page
      window.location.href = '/messages'
    } else if (activeTab === 'notifications') {
      // For notifications, handle based on the data content
      if (item.data?.url) {
        window.location.href = item.data.url
      } else if (item.data?.diary_entry_id) {
        window.location.href = `/d/${item.data.diary_entry_id}`
      } else if (item.data?.sender_id) {
        window.location.href = `/u/${item.data.sender_id}`
      }
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />
      
      {/* Panel */}
      <div className="fixed top-16 right-4 w-96 max-w-[calc(100vw-2rem)] bg-white rounded-2xl shadow-2xl border border-gray-100 z-50 max-h-[80vh] flex flex-col">
        
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-serif text-gray-800">Correspondence</h2>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ×
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-1 bg-gray-50 rounded-lg p-1">
            {[
              { key: 'messages', label: 'Messages', icon: '💬' },
              { key: 'notifications', label: 'Notifications', icon: '🔔' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.key
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
            </div>
          ) : currentItems.length > 0 ? (
              <div className="p-2">
              {currentItems.map((item) => (
                  <div
                  key={item.id}
                  className={`p-4 m-2 rounded-xl border transition-all hover:shadow-sm cursor-pointer ${
                    item.read_at
                      ? 'bg-gray-50 border-gray-100 hover:bg-gray-100'
                      : 'bg-blue-50 border-blue-100 hover:bg-blue-100'
                  }`}
                  onClick={() => handleItemClick(item)}
                >
                  <div className="flex items-start gap-3">
                    {/* Avatar or Icon */}
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center flex-shrink-0">
                      {item.sender?.avatar ? (
                        <img
                          src={item.sender.avatar}
                          alt={item.sender.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-lg">
                          {item.type === 'moment' ? '✨' : item.type === 'letter' ? '💌' : '📝'}
                        </span>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-800 text-sm mb-1">
                        {item.title}
                      </h4>

                      {/* Show photo if it exists */}
                      {item.data?.photo_url && (
                        <div className="mb-2">
                          <img
                            src={item.data.photo_url}
                            alt="Message photo"
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        </div>
                      )}

                      <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                        {item.message}
                      </p>

                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{formatTimeAgo(item.created_at)}</span>
                        {!item.read_at && (
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                ))}
              </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-2xl">
                  {activeTab === 'moments' ? '✨' : activeTab === 'letters' ? '💌' : '📝'}
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No {activeTab} yet
              </h3>
              <p className="text-gray-500 text-sm">
                {activeTab === 'messages'
                  ? 'No messages to show'
                  : 'No notifications available'
                }
              </p>
            </div>
          )}
        </div>
      </div>

    </>
  )
}
