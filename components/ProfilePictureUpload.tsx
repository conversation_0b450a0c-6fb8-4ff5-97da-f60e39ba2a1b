"use client"

import { useState, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import Image from "next/image"

interface ProfilePictureUploadProps {
  currentPictureUrl?: string
  onPictureChange?: (url: string) => void
  userId: string
}

export function ProfilePictureUpload({ 
  currentPictureUrl, 
  onPictureChange, 
  userId 
}: ProfilePictureUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState("")
  const [previewUrl, setPreviewUrl] = useState(currentPictureUrl || "")
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const supabase = createSupabaseClient()

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent default behavior that might cause navigation on mobile
    event.preventDefault()
    event.stopPropagation()

    const file = event.target.files?.[0]
    if (!file) {
      console.log('ProfilePictureUpload: No file selected')
      return
    }

    // Enhanced debugging for mobile
    console.log('ProfilePictureUpload: File selected', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      userAgent: navigator.userAgent,
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      userId: userId
    })

    // Validate file
    if (!file.type.startsWith('image/')) {
      const errorMsg = `Invalid file type: ${file.type}. Please select an image file.`
      console.error('ProfilePictureUpload:', errorMsg)
      setError(errorMsg)
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      const errorMsg = `File too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum size is 5MB.`
      console.error('ProfilePictureUpload:', errorMsg)
      setError(errorMsg)
      return
    }

    setUploading(true)
    setError("Starting upload...")

    try {
      console.log('ProfilePictureUpload: Starting upload process')

      // Create unique filename with better extension handling
      const fileExt = file.name.split('.').pop()?.toLowerCase() || 'jpg'
      const fileName = `${userId}/profile.${fileExt}`

      console.log('ProfilePictureUpload: Generated filename:', fileName)

      // Delete old profile picture if it exists
      if (currentPictureUrl) {
        console.log('ProfilePictureUpload: Removing old picture:', currentPictureUrl)
        const oldPath = currentPictureUrl.split('/').pop()
        if (oldPath) {
          const { error: removeError } = await supabase.storage
            .from('profile-pictures')
            .remove([`${userId}/${oldPath}`])

          if (removeError) {
            console.warn('ProfilePictureUpload: Failed to remove old picture:', removeError)
          } else {
            console.log('ProfilePictureUpload: Old picture removed successfully')
          }
        }
      }

      console.log('ProfilePictureUpload: Uploading to Supabase storage...')
      setError("Uploading image...")

      // Upload new profile picture with enhanced error handling
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
          upsert: true, // Replace if exists
          cacheControl: '3600'
        })

      console.log('ProfilePictureUpload: Upload response:', { uploadData, uploadError })

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`)
      }

      setError("Processing image...")

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(fileName)

      console.log('ProfilePictureUpload: Generated public URL:', publicUrl)

      // Update user profile in database
      console.log('ProfilePictureUpload: Updating user profile in database...')
      setError("Updating profile...")

      const { error: updateError } = await supabase
        .from('users')
        .update({ profile_picture_url: publicUrl })
        .eq('id', userId)

      if (updateError) {
        console.error('ProfilePictureUpload: Database update failed:', updateError)
        throw new Error(`Failed to update profile: ${updateError.message}`)
      }

      console.log('ProfilePictureUpload: Upload completed successfully')
      setPreviewUrl(publicUrl)
      onPictureChange?.(publicUrl)
      setError("") // Clear progress messages

    } catch (err: any) {
      const errorMessage = err.message || "Failed to upload profile picture"
      console.error('ProfilePictureUpload: Upload failed:', err)
      setError(errorMessage)
    } finally {
      setUploading(false)
      // Clear the file input to allow re-selection of the same file
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemovePicture = async () => {
    if (!currentPictureUrl) return

    setUploading(true)
    setError("")

    try {
      // Remove from storage
      const oldPath = currentPictureUrl.split('/').pop()
      if (oldPath) {
        await supabase.storage
          .from('profile-pictures')
          .remove([`${userId}/${oldPath}`])
      }

      // Update database
      const { error: updateError } = await supabase
        .from('users')
        .update({ profile_picture_url: null })
        .eq('id', userId)

      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`)
      }

      setPreviewUrl("")
      onPictureChange?.("")

    } catch (err: any) {
      setError(err.message || "Failed to remove profile picture")
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* Current/Preview Picture */}
      <div className="flex items-center gap-4">
        <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
          {previewUrl ? (
            <Image
              src={previewUrl}
              alt="Profile picture"
              width={80}
              height={80}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-gray-400 text-2xl">👤</div>
          )}
        </div>
        
        <div className="flex flex-col gap-2">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              fileInputRef.current?.click()
            }}
            disabled={uploading}
            className="bg-gray-800 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 disabled:opacity-50 transition-colors text-sm min-h-[44px] touch-manipulation"
          >
            {uploading ? "Uploading..." : previewUrl ? "Change Picture" : "Upload Picture"}
          </button>
          
          {previewUrl && (
            <button
              onClick={handleRemovePicture}
              disabled={uploading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 disabled:opacity-50 transition-colors text-sm"
            >
              Remove Picture
            </button>
          )}
        </div>
      </div>

      {/* Error/Progress Message */}
      {error && (
        <div className={`p-3 border rounded-lg ${
          uploading
            ? 'bg-blue-50 border-blue-200'
            : 'bg-red-50 border-red-200'
        }`}>
          <p className={`text-sm ${
            uploading
              ? 'text-blue-600'
              : 'text-red-600'
          }`}>
            {error}
          </p>
        </div>
      )}

      {/* Guidelines */}
      <div className="text-xs text-gray-500">
        <p>• Maximum file size: 5MB</p>
        <p>• Supported formats: JPG, PNG, GIF</p>
        <p>• Recommended: Square images work best</p>
        {/* Mobile debugging info */}
        {/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (
          <p className="mt-2 text-blue-600">
            📱 Mobile detected - Enhanced debugging enabled
          </p>
        )}
      </div>
    </div>
  )
}
