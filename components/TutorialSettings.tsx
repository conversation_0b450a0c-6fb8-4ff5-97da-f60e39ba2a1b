'use client'

import React, { useState } from 'react'
import { useTutorial } from '@/contexts/TutorialContext'
import { Button } from '@/components/ui/button'
import { 
  Settings, 
  RotateCcw, 
  Eye, 
  EyeOff, 
  Play, 
  Pause,
  HelpCircle,
  CheckCircle,
  X
} from 'lucide-react'
import { tutorialCategories, getCategoryProgress } from '@/lib/tutorial-steps'

interface TutorialSettingsProps {
  isOpen: boolean
  onClose: () => void
}

export function TutorialSettings({ isOpen, onClose }: TutorialSettingsProps) {
  const {
    state,
    updatePreferences,
    reset,
    startCategory,
    getProgress,
    isCategoryComplete
  } = useTutorial()

  const [isResetting, setIsResetting] = useState(false)

  const handleReset = async () => {
    setIsResetting(true)
    try {
      reset()
      // Small delay for user feedback
      await new Promise(resolve => setTimeout(resolve, 500))
    } finally {
      setIsResetting(false)
    }
  }

  const handleTogglePreference = (key: keyof typeof state.userPreferences) => {
    updatePreferences({
      [key]: !state.userPreferences[key]
    })
  }

  const progress = getProgress()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[9998] bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl border border-gray-200 w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <Settings size={20} className="text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">Tutorial Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100"
          >
            <X size={18} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Progress Overview */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-200">
            <h3 className="font-medium text-blue-900 mb-2">Your Progress</h3>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-blue-700">
                {progress.completed} of {progress.total} steps completed
              </span>
              <span className="text-sm font-medium text-blue-800">
                {progress.percentage}%
              </span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.percentage}%` }}
              />
            </div>
          </div>

          {/* Preferences */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900">Preferences</h3>
            
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {state.userPreferences.showTutorial ? (
                    <Eye size={16} className="text-green-600" />
                  ) : (
                    <EyeOff size={16} className="text-gray-400" />
                  )}
                  <span className="text-sm text-gray-700">Show tutorial system</span>
                </div>
                <button
                  onClick={() => handleTogglePreference('showTutorial')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    state.userPreferences.showTutorial ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      state.userPreferences.showTutorial ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </label>

              <label className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {state.userPreferences.autoStart ? (
                    <Play size={16} className="text-green-600" />
                  ) : (
                    <Pause size={16} className="text-gray-400" />
                  )}
                  <span className="text-sm text-gray-700">Auto-start for new features</span>
                </div>
                <button
                  onClick={() => handleTogglePreference('autoStart')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    state.userPreferences.autoStart ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      state.userPreferences.autoStart ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </label>

              <label className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <HelpCircle size={16} className="text-blue-600" />
                  <span className="text-sm text-gray-700">Show helpful hints</span>
                </div>
                <button
                  onClick={() => handleTogglePreference('showHints')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    state.userPreferences.showHints ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      state.userPreferences.showHints ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </label>
            </div>
          </div>

          {/* Category Management */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900">Tutorial Categories</h3>
            <div className="space-y-2">
              {tutorialCategories.map((category) => {
                const categoryProgress = getCategoryProgress(category.id, state.completedSteps)
                const isComplete = isCategoryComplete(category.id)
                
                return (
                  <div
                    key={category.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`
                        w-8 h-8 rounded-full flex items-center justify-center text-white text-sm
                        ${isComplete ? 'bg-green-500' : 'bg-gray-400'}
                      `}>
                        {isComplete ? <CheckCircle size={16} /> : category.icon}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{category.name}</p>
                        <p className="text-xs text-gray-500">
                          {categoryProgress.completed}/{categoryProgress.total} steps
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        startCategory(category.id)
                        onClose()
                      }}
                      className="text-xs h-7 px-2"
                    >
                      {isComplete ? 'Review' : 'Start'}
                    </Button>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Reset Section */}
          <div className="border-t border-gray-200 pt-4">
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <h4 className="font-medium text-red-900 mb-2">Reset Tutorial</h4>
              <p className="text-sm text-red-700 mb-3">
                This will clear all your progress and reset tutorial preferences to defaults.
              </p>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleReset}
                isLoading={isResetting}
                className="text-xs h-7"
              >
                <RotateCcw size={12} className="mr-1" />
                Reset All Progress
              </Button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-100">
          <Button
            onClick={onClose}
            variant="outline"
            size="sm"
            className="text-xs h-7"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  )
}

// Hook for easier usage
export function useTutorialSettings() {
  const [isOpen, setIsOpen] = useState(false)

  const openSettings = () => setIsOpen(true)
  const closeSettings = () => setIsOpen(false)

  return {
    isOpen,
    openSettings,
    closeSettings,
    TutorialSettingsModal: () => (
      <TutorialSettings isOpen={isOpen} onClose={closeSettings} />
    )
  }
}
