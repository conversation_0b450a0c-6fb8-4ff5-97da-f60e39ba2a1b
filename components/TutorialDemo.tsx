'use client'

import React from 'react'
import { useTutorial } from '@/contexts/TutorialContext'
import { Button } from '@/components/ui/button'
import { Play, RotateCcw, Settings } from 'lucide-react'
import { tutorialCategories } from '@/lib/tutorial-steps'

/**
 * Demo component to showcase the tutorial system
 * This can be used for testing or as a quick access panel for admins
 */
export function TutorialDemo() {
  const {
    state,
    startTutorial,
    startCategory,
    stopTutorial,
    reset,
    getProgress,
    isCategoryComplete
  } = useTutorial()

  const progress = getProgress()

  return (
    <div className="fixed top-4 left-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
      <div className="mb-4">
        <h3 className="font-semibold text-gray-900 text-sm mb-1">Tutorial System Demo</h3>
        <p className="text-xs text-gray-500">
          Progress: {progress.completed}/{progress.total} ({progress.percentage}%)
        </p>
        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
          <div
            className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
            style={{ width: `${progress.percentage}%` }}
          />
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <Button
          size="sm"
          onClick={() => startTutorial('welcome')}
          className="w-full text-xs h-7"
          disabled={state.isActive}
        >
          <Play size={12} className="mr-1" />
          Start Welcome Tour
        </Button>

        <div className="grid grid-cols-2 gap-1">
          {tutorialCategories.slice(1).map((category) => (
            <Button
              key={category.id}
              size="sm"
              variant="outline"
              onClick={() => startCategory(category.id)}
              disabled={state.isActive}
              className={`text-xs h-6 px-2 ${
                isCategoryComplete(category.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              {category.icon} {category.name.split(' ')[0]}
            </Button>
          ))}
        </div>
      </div>

      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={stopTutorial}
          disabled={!state.isActive}
          className="flex-1 text-xs h-6"
        >
          Stop
        </Button>
        <Button
          size="sm"
          variant="destructive"
          onClick={reset}
          className="flex-1 text-xs h-6"
        >
          <RotateCcw size={10} className="mr-1" />
          Reset
        </Button>
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Status: {state.isActive ? 'Active' : 'Inactive'} | 
        Visible: {state.isVisible ? 'Yes' : 'No'}
      </div>
    </div>
  )
}
