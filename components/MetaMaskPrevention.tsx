'use client'

import { useEffect } from 'react'

/**
 * MetaMask Prevention Component
 * 
 * Prevents MetaMask and other crypto wallet extensions from auto-detecting
 * and attempting to connect to our badge system, which uses crypto-adjacent
 * terminology but is not a blockchain application.
 * 
 * This component should be included early in the app layout to prevent
 * wallet extensions from injecting connection scripts.
 */
export function MetaMaskPrevention() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Prevent MetaMask auto-detection by blocking ethereum object
    try {
      // Define ethereum as undefined and make it non-writable
      Object.defineProperty(window, 'ethereum', {
        value: undefined,
        writable: false,
        configurable: false
      })

      // Also block common wallet detection patterns
      Object.defineProperty(window, 'web3', {
        value: undefined,
        writable: false,
        configurable: false
      })

      // Block wallet connection attempts
      const originalAddEventListener = window.addEventListener
      window.addEventListener = function(type, listener, options) {
        // Block wallet-related events
        if (type === 'ethereum#initialized' || 
            type === 'eip6963:announceProvider' ||
            type.includes('wallet') ||
            type.includes('metamask')) {
          console.log('Blocked wallet event:', type)
          return
        }
        return originalAddEventListener.call(this, type, listener, options)
      }

      console.log('✅ MetaMask prevention activated - Badge system protected')
    } catch (error) {
      // Silently handle any errors - some extensions might have already injected
      console.log('MetaMask prevention: Extension may have already loaded')
    }

    // Cleanup function
    return () => {
      // Note: We don't restore the ethereum object as it should stay blocked
      console.log('MetaMask prevention cleanup completed')
    }
  }, [])

  // This component renders nothing - it's purely for side effects
  return null
}
