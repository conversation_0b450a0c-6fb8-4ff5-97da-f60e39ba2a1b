import { getReactionDisplay, ReactionData } from '@/lib/reaction-utils'

interface ReactionDisplayProps {
  reactions: ReactionData[]
  onClick?: () => void
  className?: string
}

/**
 * Component for displaying reaction count with most popular emoji
 */
export function ReactionDisplay({ 
  reactions, 
  onClick, 
  className = "cursor-pointer hover:underline" 
}: ReactionDisplayProps) {
  const { emoji, totalCount } = getReactionDisplay(reactions)

  if (totalCount === 0) {
    return null
  }

  return (
    <span 
      className={className} 
      onClick={onClick}
      title="View reactions"
    >
      {emoji} {totalCount}
    </span>
  )
}
