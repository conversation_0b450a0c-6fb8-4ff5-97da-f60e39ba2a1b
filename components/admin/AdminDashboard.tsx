'use client'

import React, { useState, useEffect } from 'react'
import { CreatorsList } from './CreatorsList'
import { PaymentActivity } from './PaymentActivity'
import { AdminStats } from './AdminStats'

interface Creator {
  id: string
  name: string
  email: string
  role: string
  stripe_account_id: string | null
  stripe_onboarding_complete: boolean
  price_monthly: number | null
  created_at: string
  updated_at: string
}

interface SubscriptionStat {
  writer_id: string
  status: string
  created_at: string
}

interface PaymentActivity {
  id: string
  writer_id: string
  amount: number
  status: string
  payment_type: string
  created_at: string
  users: { name: string } | null
}

interface AdminDashboardProps {
  creators: Creator[]
  subscriptionStats: SubscriptionStat[]
  recentActivity: PaymentActivity[]
}

export function AdminDashboard({ creators, subscriptionStats, recentActivity }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'creators' | 'payments' | 'disputes' | 'changelog'>('overview')
  const [changelogStatus, setChangelogStatus] = useState<{exists: boolean, user?: any, entryCount?: number} | null>(null)
  const [setupLoading, setSetupLoading] = useState(false)

  // Calculate stats
  const totalCreators = creators.length
  const activeCreators = creators.filter(c => c.stripe_onboarding_complete).length
  const pendingSetup = creators.filter(c => c.stripe_account_id && !c.stripe_onboarding_complete).length
  const totalRevenue = recentActivity
    .filter(p => p.status === 'completed')
    .reduce((sum, p) => sum + p.amount, 0)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'creators', label: 'Creators', icon: '👥' },
    { id: 'payments', label: 'Payments', icon: '💳' },
    { id: 'disputes', label: 'Disputes', icon: '⚠️' },
    { id: 'changelog', label: 'The Code Book', icon: '📝' }
  ]

  // ChangeLog setup functions
  const checkChangelogStatus = async () => {
    try {
      const response = await fetch('/api/admin/setup-changelog')
      const data = await response.json()
      setChangelogStatus(data)
    } catch (error) {
      console.error('Error checking changelog status:', error)
    }
  }

  const setupChangelog = async () => {
    setSetupLoading(true)
    try {
      const response = await fetch('/api/admin/setup-changelog', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        alert('ChangeLog user created successfully!')
        await checkChangelogStatus()
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error setting up changelog:', error)
      alert('Failed to setup changelog')
    } finally {
      setSetupLoading(false)
    }
  }

  // Check changelog status when tab is selected
  useEffect(() => {
    if (activeTab === 'changelog' && !changelogStatus) {
      checkChangelogStatus()
    }
  }, [activeTab, changelogStatus])

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <AdminStats 
        totalCreators={totalCreators}
        activeCreators={activeCreators}
        pendingSetup={pendingSetup}
        totalRevenue={totalRevenue}
      />

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="flex items-center gap-2">
                  <span>{tab.icon}</span>
                  {tab.label}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Platform Overview</h3>
              
              {/* Recent Activity Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Recent Signups</h4>
                  <div className="space-y-2">
                    {creators.slice(0, 5).map((creator) => (
                      <div key={creator.id} className="flex justify-between text-sm">
                        <span>{creator.name}</span>
                        <span className="text-gray-500">
                          {new Date(creator.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Setup Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>✅ Fully Setup</span>
                      <span className="font-medium">{activeCreators}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>⏳ Pending Setup</span>
                      <span className="font-medium">{pendingSetup}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>❌ Not Started</span>
                      <span className="font-medium">{totalCreators - activeCreators - pendingSetup}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'creators' && (
            <CreatorsList creators={creators} />
          )}

          {activeTab === 'payments' && (
            <PaymentActivity recentActivity={recentActivity} />
          )}

          {activeTab === 'disputes' && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🚧</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Dispute Management</h3>
              <p className="text-gray-600">
                Dispute tracking will be available once Stripe webhooks are configured.
              </p>
            </div>
          )}

          {activeTab === 'changelog' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">The Code Book Management</h3>
                <a
                  href="/changelog"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View Code Book Page →
                </a>
              </div>

              {changelogStatus === null ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-600 mt-2">Checking ChangeLog status...</p>
                </div>
              ) : changelogStatus.exists ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-lg">✅</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-green-900">Code Book User Active</h4>
                      <p className="text-green-700 text-sm">The OnlyDiary Code Book user is set up and ready.</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="bg-white rounded-lg p-3 border border-green-200">
                      <div className="font-medium text-gray-900">User ID</div>
                      <div className="text-gray-600 font-mono text-xs">{changelogStatus.user?.id}</div>
                    </div>
                    <div className="bg-white rounded-lg p-3 border border-green-200">
                      <div className="font-medium text-gray-900">Entries Published</div>
                      <div className="text-gray-600">{changelogStatus.entryCount || 0}</div>
                    </div>
                    <div className="bg-white rounded-lg p-3 border border-green-200">
                      <div className="font-medium text-gray-900">Created</div>
                      <div className="text-gray-600">
                        {changelogStatus.user?.created_at ?
                          new Date(changelogStatus.user.created_at).toLocaleDateString() :
                          'Unknown'
                        }
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">How to Add Code Book Entries</h5>
                    <ol className="text-blue-800 text-sm space-y-1 list-decimal list-inside">
                      <li>Go to the <a href="/write/diary" className="underline hover:text-blue-900">Write Diary</a> page</li>
                      <li>Switch to the "OnlyDiary ChangeLog" account (you'll need to implement account switching)</li>
                      <li>Write your Code Book entry in plain language with technical details</li>
                      <li>Mark as free (recommended) or paid for premium insights</li>
                      <li>Publish - subscribers will see it in their timeline</li>
                    </ol>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                      <span className="text-yellow-600 text-lg">⚠️</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-yellow-900">Code Book Not Set Up</h4>
                      <p className="text-yellow-700 text-sm">Create the OnlyDiary Code Book user account to start publishing updates.</p>
                    </div>
                  </div>

                  <button
                    onClick={setupChangelog}
                    disabled={setupLoading}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {setupLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Setting up...
                      </>
                    ) : (
                      <>
                        <span>📝</span>
                        Create Code Book User
                      </>
                    )}
                  </button>

                  <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">What this will create:</h5>
                    <ul className="text-gray-700 text-sm space-y-1 list-disc list-inside">
                      <li>A special "OnlyDiary ChangeLog" user account</li>
                      <li>A welcome Code Book entry explaining the feature</li>
                      <li>Integration with the subscription system</li>
                      <li>Timeline visibility for subscribers</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
