"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface SubscribeButtonProps {
  writerId: string
  price?: number
}

export function SubscribeButton({ writerId }: SubscribeButtonProps) {
  const [loading, setLoading] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [checkingStatus, setCheckingStatus] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkFollowStatus()
  }, [writerId])

  const checkFollowStatus = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setCheckingStatus(false)
        return
      }

      // Check if user is following (for free subscriptions like Code Book)
      const { data: followData } = await supabase
        .from("follows")
        .select("id")
        .eq("follower_id", user.id)
        .eq("writer_id", writerId)
        .single()

      // Check if user has active subscription (for paid subscriptions)
      const { data: subscriptionData } = await supabase
        .from("subscriptions")
        .select("id")
        .eq("subscriber_id", user.id)
        .eq("writer_id", writerId)
        .gte("active_until", new Date().toISOString())
        .single()

      setIsFollowing(!!(followData || subscriptionData))
    } catch (error) {
      // User not following/subscribed
      setIsFollowing(false)
    } finally {
      setCheckingStatus(false)
    }
  }

  const handleSubscribe = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ writerId }),
      })
      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else if (data.success) {
        // Free subscription successful
        setIsFollowing(true)
        if (data.redirect) {
          window.location.href = data.redirect
        }
      } else {
        alert(data.error || 'Failed to create subscription')
      }
    } catch {
      alert('Failed to create subscription')
    } finally {
      setLoading(false)
    }
  }

  if (checkingStatus) {
    return (
      <button
        disabled
        className="bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
      >
        Loading...
      </button>
    )
  }

  if (isFollowing) {
    return (
      <button
        disabled
        className="bg-green-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
      >
        Following
      </button>
    )
  }

  return (
    <button
      onClick={handleSubscribe}
      disabled={loading}
      className="bg-gray-800 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
    >
      {loading ? 'Loading...' : 'Subscribe'}
    </button>
  )
}