"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface ProcessingStep {
  id: string
  label: string
  status: 'pending' | 'processing' | 'complete' | 'error'
  details?: string
}

interface ProcessingStatusProps {
  isVisible: boolean
  fileType: 'pdf' | 'epub'
  onComplete?: (success: boolean) => void
}

export function ProcessingStatus({ isVisible, fileType, onComplete }: ProcessingStatusProps) {
  const [steps, setSteps] = useState<ProcessingStep[]>([
    { id: 'upload', label: 'Uploading file', status: 'pending' },
    { id: 'extract', label: 'Extracting content', status: 'pending' },
    { id: 'analyze', label: 'Analyzing text', status: 'pending' },
    { id: 'chapters', label: 'Detecting chapters', status: 'pending' },
    { id: 'metadata', label: 'Extracting metadata', status: 'pending' },
    { id: 'finalize', label: 'Finalizing book', status: 'pending' }
  ])

  const [currentStep, setCurrentStep] = useState(0)
  const [processingComplete, setProcessingComplete] = useState(false)
  const [countdown, setCountdown] = useState(3)

  useEffect(() => {
    if (!isVisible) return

    // Simulate processing steps
    const processSteps = async () => {
      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(i)
        
        // Update current step to processing
        setSteps(prev => prev.map((step, index) => 
          index === i ? { ...step, status: 'processing' } : step
        ))

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

        // Mark as complete
        setSteps(prev => prev.map((step, index) => 
          index === i ? { ...step, status: 'complete' } : step
        ))
      }

      setProcessingComplete(true)

      // Start countdown
      let timeLeft = 3
      const countdownInterval = setInterval(() => {
        timeLeft--
        setCountdown(timeLeft)

        if (timeLeft <= 0) {
          clearInterval(countdownInterval)
          onComplete?.(true)
        }
      }, 1000)
    }

    processSteps()
  }, [isVisible])

  if (!isVisible) return null

  const getStepIcon = (status: ProcessingStep['status']) => {
    switch (status) {
      case 'pending':
        return '⏳'
      case 'processing':
        return '🔄'
      case 'complete':
        return '✅'
      case 'error':
        return '❌'
      default:
        return '⏳'
    }
  }

  const getStepColor = (status: ProcessingStep['status']) => {
    switch (status) {
      case 'pending':
        return 'text-gray-500'
      case 'processing':
        return 'text-blue-600'
      case 'complete':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-500'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <div className="text-4xl mb-2">📚</div>
            <h3 className="text-lg font-semibold text-gray-900">
              Processing {fileType.toUpperCase()} File
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Extracting content and analyzing your book...
            </p>
          </div>

          <div className="space-y-3">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <span className="text-lg">
                    {getStepIcon(step.status)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className={`text-sm font-medium ${getStepColor(step.status)}`}>
                    {step.label}
                  </div>
                  {step.details && (
                    <div className="text-xs text-gray-500 mt-1">
                      {step.details}
                    </div>
                  )}
                </div>
                {step.status === 'processing' && (
                  <div className="flex-shrink-0">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {processingComplete && (
            <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center">
                <span className="text-green-600 text-lg mr-2">🎉</span>
                <div>
                  <div className="text-sm font-medium text-green-800">
                    Processing Complete!
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    Your book has been successfully processed and is ready for readers.
                    {countdown > 0 && (
                      <span className="block mt-1 font-medium">
                        Redirecting in {countdown} seconds...
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6">
            <div className="bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 text-center mt-2">
              Step {currentStep + 1} of {steps.length}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ProcessingStatus
