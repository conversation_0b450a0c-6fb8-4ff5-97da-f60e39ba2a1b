"use client"

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface Contact {
  name: string
  phone?: string
  email?: string
}

interface ExistingUser {
  id: string
  name: string
  phone?: string
  email?: string
  profile_picture_url?: string
}

export function InviteSystem() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [existingUsers, setExistingUsers] = useState<ExistingUser[]>([])
  const [invitesSent, setInvitesSent] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user: authUser } } = await supabase.auth.getUser()
    if (authUser) {
      const { data: profile } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.id)
        .single()
      setUser(profile)
    }
  }

  const importContacts = async () => {
    if (!('contacts' in navigator)) {
      alert('Contact import not supported on this device. Try the share link instead!')
      return
    }

    try {
      setLoading(true)
      // @ts-ignore - Contact API is experimental
      const contacts = await navigator.contacts.select(['name', 'tel', 'email'], { multiple: true })
      
      const formattedContacts = contacts.map((contact: any) => ({
        name: contact.name?.[0] || 'Unknown',
        phone: contact.tel?.[0] || '',
        email: contact.email?.[0] || ''
      }))

      setContacts(formattedContacts)
      await findExistingUsers(formattedContacts)
    } catch (error) {
      console.error('Error importing contacts:', error)
      alert('Could not import contacts. Try sharing your invite link instead!')
    } finally {
      setLoading(false)
    }
  }

  const findExistingUsers = async (contactList: Contact[]) => {
    const phones = contactList.map(c => c.phone).filter(Boolean)
    const emails = contactList.map(c => c.email).filter(Boolean)

    if (phones.length === 0 && emails.length === 0) return

    const { data: users } = await supabase
      .from('users')
      .select('id, name, phone, email, profile_picture_url')
      .or(`phone.in.(${phones.join(',')}),email.in.(${emails.join(',')})`)

    setExistingUsers(users || [])
  }

  const sendInvite = async (contact: Contact, method: 'sms' | 'email' | 'share') => {
    const inviteCode = `${user?.name?.replace(/\s+/g, '')}${Date.now()}`
    const inviteLink = `https://onlydiary.app/invite/${inviteCode}`
    const message = `Hey! I'm sharing my personal stories on OnlyDiary. Check it out: ${inviteLink}`

    if (method === 'sms' && contact.phone) {
      window.open(`sms:${contact.phone}?body=${encodeURIComponent(message)}`)
    } else if (method === 'email' && contact.email) {
      window.open(`mailto:${contact.email}?subject=Join me on OnlyDiary&body=${encodeURIComponent(message)}`)
    } else if (method === 'share') {
      if (navigator.share) {
        await navigator.share({
          title: 'Join me on OnlyDiary',
          text: message,
          url: inviteLink
        })
      } else {
        await navigator.clipboard.writeText(message)
        alert('Invite link copied to clipboard!')
      }
    }

    // Track invite
    await supabase.from('invites').insert({
      inviter_id: user?.id,
      invite_code: inviteCode,
      contact_name: contact.name,
      contact_phone: contact.phone,
      contact_email: contact.email,
      method: method
    })

    setInvitesSent(prev => [...prev, contact.phone || contact.email || contact.name])
  }

  const getInviteRewards = () => {
    const inviteCount = invitesSent.length
    if (inviteCount >= 10) return "🎉 $50 credit + Premium features!"
    if (inviteCount >= 5) return "🎁 $25 credit earned!"
    if (inviteCount >= 3) return "💰 $15 credit earned!"
    if (inviteCount >= 1) return "✨ $5 credit earned!"
    return "Invite friends to earn credits!"
  }

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-serif text-gray-800 mb-2">Invite Friends</h2>
        <p className="text-gray-600">Share OnlyDiary and earn credits when friends join!</p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
          <p className="text-blue-700 font-medium">{getInviteRewards()}</p>
        </div>
      </div>

      {/* Existing Users Found */}
      {existingUsers.length > 0 && (
        <div className="mb-6">
          <h3 className="font-medium text-gray-900 mb-3">
            🎉 {existingUsers.length} friends already on OnlyDiary!
          </h3>
          <div className="space-y-2">
            {existingUsers.map((user) => (
              <div key={user.id} className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden">
                  {user.profile_picture_url ? (
                    <img src={user.profile_picture_url} alt={user.name} className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-500 font-serif">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-gray-900">{user.name}</p>
                    {user.has_day1_badge && (
                      <Day1Badge
                        signupNumber={user.signup_number}
                        badgeTier={user.badge_tier}
                        size="sm"
                        className="flex-shrink-0"
                      />
                    )}
                  </div>
                  <p className="text-sm text-green-600">Already on OnlyDiary!</p>
                </div>
                <button
                  onClick={() => window.location.href = `/u/${user.id}`}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                >
                  Follow
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Import Contacts */}
      <div className="space-y-4">
        <button
          onClick={importContacts}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Importing Contacts...' : '📱 Import Contacts'}
        </button>

        <div className="text-center">
          <span className="text-gray-500 text-sm">or</span>
        </div>

        <button
          onClick={() => sendInvite({ name: 'Share Link' }, 'share')}
          className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200"
        >
          📤 Share Invite Link
        </button>
      </div>

      {/* Contact List */}
      {contacts.length > 0 && (
        <div className="mt-6">
          <h3 className="font-medium text-gray-900 mb-3">Your Contacts</h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {contacts.slice(0, 20).map((contact, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{contact.name}</p>
                  <p className="text-sm text-gray-500">{contact.phone || contact.email}</p>
                </div>
                <div className="flex gap-2">
                  {contact.phone && (
                    <button
                      onClick={() => sendInvite(contact, 'sms')}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                    >
                      SMS
                    </button>
                  )}
                  {contact.email && (
                    <button
                      onClick={() => sendInvite(contact, 'email')}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                    >
                      Email
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
