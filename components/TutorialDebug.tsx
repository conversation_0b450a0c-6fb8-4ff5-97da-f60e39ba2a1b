'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'

/**
 * Simple debug component to test tutorial system
 * Add this to any page to force-show tutorial controls
 */
export function TutorialDebug() {
  const [mounted, setMounted] = useState(false)
  const [tutorialState, setTutorialState] = useState<any>(null)

  useEffect(() => {
    setMounted(true)
    
    // Check if tutorial context is available
    try {
      const saved = localStorage.getItem('onlydiary_tutorial_state')
      setTutorialState(saved ? JSON.parse(saved) : null)
    } catch (error) {
      console.error('Tutorial debug error:', error)
    }
  }, [])

  const forceTutorial = () => {
    // Force enable tutorial system
    const tutorialState = {
      isActive: false,
      isVisible: true,
      currentStep: 0,
      currentCategory: null,
      completedSteps: [],
      completedCategories: [],
      hasSeenWelcome: false,
      userPreferences: {
        showTutorial: true,
        autoStart: true,
        showHints: true
      }
    }
    
    localStorage.setItem('onlydiary_tutorial_state', JSON.stringify(tutorialState))
    window.location.reload()
  }

  const startWelcomeTutorial = () => {
    // Dispatch custom event to start tutorial
    window.dispatchEvent(new CustomEvent('startTutorial', { 
      detail: { category: 'welcome' } 
    }))
  }

  if (!mounted) return null

  return (
    <div className="fixed top-4 right-4 z-[9999] bg-red-100 border-2 border-red-300 rounded-lg p-4 max-w-sm">
      <h3 className="font-bold text-red-800 mb-2">🐛 Tutorial Debug</h3>
      
      <div className="space-y-2 text-xs text-red-700 mb-3">
        <p>Tutorial State: {tutorialState ? 'Found' : 'Not Found'}</p>
        <p>Show Tutorial: {tutorialState?.userPreferences?.showTutorial ? 'Yes' : 'No'}</p>
        <p>Is Visible: {tutorialState?.isVisible ? 'Yes' : 'No'}</p>
      </div>

      <div className="space-y-2">
        <Button 
          onClick={forceTutorial}
          size="sm" 
          className="w-full text-xs h-7 bg-red-600 hover:bg-red-700"
        >
          🔧 Force Enable Tutorial
        </Button>
        
        <Button 
          onClick={startWelcomeTutorial}
          size="sm" 
          variant="outline"
          className="w-full text-xs h-7 border-red-300 text-red-700"
        >
          ▶️ Start Welcome Tour
        </Button>
      </div>

      <p className="text-xs text-red-600 mt-2">
        Remove this component in production!
      </p>
    </div>
  )
}

/**
 * Simple floating tutorial button for immediate testing
 */
export function SimpleTutorialButton() {
  const [showTutorial, setShowTutorial] = useState(false)

  const toggleTutorial = () => {
    setShowTutorial(!showTutorial)
  }

  return (
    <>
      {/* Simple floating button */}
      <button
        onClick={toggleTutorial}
        className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
        title="Tutorial Help"
      >
        <span className="text-xl">?</span>
      </button>

      {/* Simple tutorial overlay */}
      {showTutorial && (
        <div className="fixed inset-0 z-[9999] bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-2xl border border-gray-200 w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Welcome to OnlyDiary! 👋</h2>
              <button
                onClick={toggleTutorial}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                ✕
              </button>
            </div>
            
            <p className="text-gray-700 mb-4">
              OnlyDiary is a new kind of social network built on authentic diary entries instead of posts. 
              Let's explore what makes this platform special.
            </p>

            <div className="flex gap-2">
              <Button 
                onClick={toggleTutorial}
                variant="outline" 
                size="sm"
                className="flex-1"
              >
                Skip
              </Button>
              <Button 
                onClick={toggleTutorial}
                size="sm"
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                Next Step
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
