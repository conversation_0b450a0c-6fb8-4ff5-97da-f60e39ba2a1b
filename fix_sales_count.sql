-- Fix sales count for all books based on actual purchases
-- This will update the sales_count in projects table to match actual book_purchases

UPDATE projects 
SET sales_count = (
    SELECT COUNT(*) 
    FROM book_purchases 
    WHERE book_purchases.project_id = projects.id
)
WHERE is_ebook = true;

-- Verify the update worked
SELECT 
    p.id,
    p.title,
    p.sales_count as updated_sales_count,
    COUNT(bp.id) as actual_purchases
FROM projects p
LEFT JOIN book_purchases bp ON p.id = bp.project_id
WHERE p.is_ebook = true
GROUP BY p.id, p.title, p.sales_count
ORDER BY p.sales_count DESC;
