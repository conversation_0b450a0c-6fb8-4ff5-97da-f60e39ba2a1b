git add . && git commit -m "Fix photo upload authentication issue - photos now appear after upload

## Photo Upload Authentication Fix
- Add session refresh before loading and uploading photos
- Ensure user authentication is maintained throughout photo operations
- Fix issue where photos were uploaded but not visible due to auth session problems

## Root Cause
- RLS policies require authenticated user session
- User session was becoming stale/null during photo operations
- Photos were uploaded successfully but couldn't be read back due to auth failure

## Technical Changes
- components/PhotoUpload.tsx: Add supabase.auth.refreshSession() before critical operations
- Refresh session before loadExistingPhotos() to ensure RLS policies work
- Refresh session before photo upload to ensure proper user_id assignment
- Improved error handling for authentication failures

## User Experience Impact
- Photos now appear immediately after upload
- No more 'upload successful but photos don't show' issue
- Better error messages when authentication fails
- Maintains user session consistency throughout photo workflow

## Files Modified
- components/PhotoUpload.tsx: Enhanced authentication handling

This resolves the issue where users could upload photos but they wouldn't appear in the UI due to authentication session problems with RLS policies."
