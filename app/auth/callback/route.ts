import { createSupabaseServerClient } from '@/lib/supabase/client'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/timeline'

  console.log('Auth callback route handler triggered')
  console.log('Code:', code ? 'present' : 'missing')
  console.log('Next:', next)

  if (code) {
    const supabase = await createSupabaseServerClient()
    
    try {
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)
      
      if (error) {
        console.error('Error exchanging code for session:', error)
        return NextResponse.redirect(`${origin}/login?error=auth_failed`)
      }

      if (data.session?.user) {
        console.log('Session created for user:', data.session.user.id)
        
        // Check if user exists in our users table, if not create them
        const { data: existingUser, error: userCheckError } = await supabase
          .from('users')
          .select('id, role')
          .eq('id', data.session.user.id)
          .single()

        if (userCheckError || !existingUser) {
          console.log('Creating new user profile for Google OAuth user')
          
          // Create user profile for Google OAuth users
          const newUserData = {
            id: data.session.user.id,
            email: data.session.user.email!,
            name: data.session.user.user_metadata?.full_name || data.session.user.user_metadata?.name || null,
            avatar: data.session.user.user_metadata?.avatar_url || null,
            profile_picture_url: data.session.user.user_metadata?.avatar_url || null,
            role: 'user' as const
          }

          const { error: insertError } = await supabase
            .from('users')
            .insert(newUserData)

          if (insertError) {
            console.error('Error creating user profile:', insertError)
            return NextResponse.redirect(`${origin}/login?error=profile_creation_failed`)
          }

          console.log('User profile created successfully')

          // Send welcome email for new Google users
          try {
            await fetch(`${origin}/api/send-welcome-email`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                userId: data.session.user.id,
                userEmail: data.session.user.email,
                userName: newUserData.name || 'New User',
                userRole: 'user'
              })
            })
          } catch (emailError) {
            console.log('Welcome email failed (non-critical):', emailError)
          }
        } else {
          console.log('Existing user found:', existingUser.id)
        }

        console.log('Redirecting to:', next)
        return NextResponse.redirect(`${origin}${next}`)
      }
    } catch (error) {
      console.error('Unexpected error in auth callback:', error)
      return NextResponse.redirect(`${origin}/login?error=unexpected_error`)
    }
  }

  // If no code or other error, redirect to login
  console.log('No code found, redirecting to login')
  return NextResponse.redirect(`${origin}/login?error=no_code`)
}
