import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { contentType, contentId, platform } = await request.json()
    
    if (!contentType || !contentId || !platform) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get user (required for share tracking)
    let user = null
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      user = authUser
    } catch (authError) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Track share
    const { error } = await supabase
      .from('share_tracking')
      .insert({
        user_id: user.id,
        content_type: contentType,
        content_id: contentId,
        platform
      })

    if (error) {
      console.error('Error tracking share:', error)
      return NextResponse.json({ error: 'Failed to track share' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in share tracking:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
