import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { contentType, contentId, source = 'timeline' } = await request.json()
    
    if (!contentType || !contentId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get user if authenticated (handle potential errors gracefully)
    let user = null
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      user = authUser
    } catch (authError) {
      console.log('No authenticated user for click tracking')
    }
    
    // Get IP address and user agent
    const ip = request.ip || 
               request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Track click
    const { error } = await supabase
      .from('post_clicks')
      .insert({
        user_id: user?.id || null,
        content_type: contentType,
        content_id: contentId,
        ip_address: ip,
        user_agent: userAgent,
        source
      })

    if (error) {
      console.error('Error tracking click:', error)
      return NextResponse.json({ error: 'Failed to track click' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in click tracking:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
