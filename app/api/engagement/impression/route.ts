import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { contentType, contentId, source = 'timeline' } = await request.json()

    if (!contentType || !contentId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get user if authenticated (handle potential errors gracefully)
    let user = null
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      user = authUser
    } catch (authError) {
      console.log('No authenticated user for impression tracking')
    }
    
    // Get IP address and user agent
    const ip = request.ip || 
               request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || 
               'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Track impression
    const { error } = await supabase
      .from('post_impressions')
      .insert({
        user_id: user?.id || null,
        content_type: contentType,
        content_id: contentId,
        ip_address: ip,
        user_agent: userAgent,
        source
      })

    // Don't return error for duplicate impressions - just ignore silently
    if (error && !error.message.includes('duplicate key')) {
      console.error('Error tracking impression:', error)
      return NextResponse.json({ error: 'Failed to track impression' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in impression tracking:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
