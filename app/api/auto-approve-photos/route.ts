import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST(request: NextRequest) {
  try {
    // REQUIRED authentication for automated endpoints
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET_TOKEN

    // Require the secret token to be set
    if (!expectedToken) {
      console.error('CRON_SECRET_TOKEN environment variable not set')
      return NextResponse.json({
        error: 'Automated endpoint authentication not configured'
      }, { status: 500 })
    }

    // Validate the authorization header format
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        error: 'Missing or invalid authorization header. Expected: Bearer <token>'
      }, { status: 401 })
    }

    // Extract and validate the token
    const providedToken = authHeader.substring(7) // Remove 'Bearer ' prefix
    if (providedToken !== expectedToken) {
      // Add a small delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000))
      return NextResponse.json({ error: 'Invalid automation token' }, { status: 401 })
    }

    const supabase = await createSupabaseServerClient()
    
    // Auto-approve photos that have been pending for more than 5 minutes
    const { error } = await supabase.rpc('auto_approve_pending_photos')
    
    if (error) {
      console.error('Error auto-approving photos:', error)
      return NextResponse.json(
        { error: "Failed to auto-approve photos" },
        { status: 500 }
      )
    }

    // Also manually approve any photos that are still pending
    const { data: pendingPhotos, error: selectError } = await supabase
      .from('photos')
      .select('id')
      .eq('moderation_status', 'pending')

    if (selectError) {
      console.error('Error selecting pending photos:', selectError)
    } else if (pendingPhotos && pendingPhotos.length > 0) {
      const { error: updateError } = await supabase
        .from('photos')
        .update({ 
          moderation_status: 'approved',
          rekognition_labels: {
            auto_approved: true,
            reason: 'Manual auto-approval via API',
            processed_at: new Date().toISOString()
          }
        })
        .eq('moderation_status', 'pending')

      if (updateError) {
        console.error('Error updating pending photos:', updateError)
      } else {
        console.log(`Auto-approved ${pendingPhotos.length} pending photos`)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Auto-approval process completed"
    })

  } catch (error) {
    console.error("Error in auto-approve endpoint:", error)
    return NextResponse.json(
      { error: "Failed to process auto-approval" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Auto-Approval API',
    description: 'Automatically approves photos that have been pending for too long'
  })
}
