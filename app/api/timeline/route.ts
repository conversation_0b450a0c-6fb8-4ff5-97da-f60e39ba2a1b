import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()

    // Fetch diary entries
    const { data: diaryEntries, error: diaryError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        created_at,
        is_free,
        love_count,
        view_count,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        ),
        photos (
          id,
          url,
          alt_text
        ),
        videos (
          id,
          r2_public_url,
          custom_thumbnail_url,
          view_count
        )
      `)
      .eq('is_hidden', false)
      .order('created_at', { ascending: false })
      .limit(20)

    if (diaryError) {
      console.error('Error fetching diary entries:', diaryError)
    }

    // Fetch audio posts
    const { data: audioPosts, error: audioError } = await supabase
      .from('audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        play_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        )
      `)
      .order('created_at', { ascending: false })
      .limit(30)

    if (audioError) {
      console.error('Error fetching audio posts:', audioError)
    }

    // Fetch book releases (completed books only)
    const { data: bookReleases, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        description,
        cover_image_url,
        genre,
        price_amount,
        average_rating,
        review_count,
        sales_count,
        created_at,
        author_name,
        is_ebook,
        slug,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        )
      `)
      .eq('is_ebook', true)
      .eq('is_complete', true)
      .eq('is_private', false)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (bookError) {
      console.error('Error fetching book releases:', bookError)
    }

    // Get book reactions if we have books
    let bookReactions: Record<string, Record<string, number>> = {}
    let userBookReactions: Record<string, string> = {}

    if (bookReleases && bookReleases.length > 0) {
      const bookIds = bookReleases.map(book => book.id)

      // Get all reactions for these books
      const { data: reactions } = await supabase
        .from('reactions')
        .select('book_id, reaction_type, user_id')
        .in('book_id', bookIds)

      // Group reactions by book and type
      reactions?.forEach(reaction => {
        if (!bookReactions[reaction.book_id]) {
          bookReactions[reaction.book_id] = {}
        }
        bookReactions[reaction.book_id][reaction.reaction_type] =
          (bookReactions[reaction.book_id][reaction.reaction_type] || 0) + 1

        // Track current user's reactions
        if (user && reaction.user_id === user.id) {
          userBookReactions[reaction.book_id] = reaction.reaction_type
        }
      })
    }

    // Get diary entry reactions
    let diaryReactions: Record<string, Record<string, number>> = {}
    let userDiaryReactions: Record<string, string> = {}

    if (diaryEntries && diaryEntries.length > 0) {
      const diaryIds = diaryEntries.map(entry => entry.id)

      const { data: reactions } = await supabase
        .from('reactions')
        .select('diary_entry_id, reaction_type, user_id')
        .in('diary_entry_id', diaryIds)

      reactions?.forEach(reaction => {
        if (!diaryReactions[reaction.diary_entry_id]) {
          diaryReactions[reaction.diary_entry_id] = {}
        }
        diaryReactions[reaction.diary_entry_id][reaction.reaction_type] =
          (diaryReactions[reaction.diary_entry_id][reaction.reaction_type] || 0) + 1

        if (user && reaction.user_id === user.id) {
          userDiaryReactions[reaction.diary_entry_id] = reaction.reaction_type
        }
      })
    }

    // Get audio post reactions
    let audioReactions: Record<string, Record<string, number>> = {}
    let userAudioReactions: Record<string, string> = {}

    if (audioPosts && audioPosts.length > 0) {
      const audioIds = audioPosts.map(post => post.id)

      const { data: reactions } = await supabase
        .from('reactions')
        .select('audio_post_id, reaction_type, user_id')
        .in('audio_post_id', audioIds)

      reactions?.forEach(reaction => {
        if (!audioReactions[reaction.audio_post_id]) {
          audioReactions[reaction.audio_post_id] = {}
        }
        audioReactions[reaction.audio_post_id][reaction.reaction_type] =
          (audioReactions[reaction.audio_post_id][reaction.reaction_type] || 0) + 1

        if (user && reaction.user_id === user.id) {
          userAudioReactions[reaction.audio_post_id] = reaction.reaction_type
        }
      })
    }

    // Get follow and subscription status if user is logged in
    let followingIds: Set<string> = new Set()
    let subscribedIds: Set<string> = new Set()

    if (user) {
      // Get follows
      const { data: follows } = await supabase
        .from('follows')
        .select('writer_id')
        .eq('follower_id', user.id)

      followingIds = new Set(follows?.map(f => f.writer_id) || [])

      // Get subscriptions
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('writer_id')
        .eq('reader_id', user.id)
        .eq('status', 'active')

      subscribedIds = new Set(subscriptions?.map(s => s.writer_id) || [])
    }

    // Show content from followed/subscribed creators, plus some general content if no follows
    let filteredBookReleases = bookReleases || []
    let filteredDiaryEntries = diaryEntries || []
    let filteredAudioPosts = audioPosts || []

    // Get Code Book user ID to filter it out from general content
    const { data: codeBookUser } = await supabase
      .from('users')
      .select('id')
      .eq('name', 'OnlyDiary ChangeLog')
      .single()

    const codeBookUserId = codeBookUser?.id

    if (user && (followingIds.size > 0 || subscribedIds.size > 0)) {
      // User has follows/subscriptions - show content from followed creators only
      filteredBookReleases = (bookReleases || []).filter(book =>
        subscribedIds.has(book.user_id) || followingIds.has(book.user_id)
      )

      // Only show diary entries from followed/subscribed creators
      filteredDiaryEntries = (diaryEntries || []).filter(entry =>
        subscribedIds.has(entry.user_id) || followingIds.has(entry.user_id)
      )

      // Only show audio posts from followed/subscribed creators
      filteredAudioPosts = (audioPosts || []).filter(post =>
        subscribedIds.has(post.user_id) || followingIds.has(post.user_id)
      )
    } else if (user) {
      // User has no follows - show recent general content (excluding Code Book)
      filteredBookReleases = (bookReleases || [])
        .filter(book => book.user_id !== codeBookUserId)
        .slice(0, 10)
      filteredDiaryEntries = (diaryEntries || [])
        .filter(entry => entry.user_id !== codeBookUserId)
        .slice(0, 15)
      filteredAudioPosts = (audioPosts || []).slice(0, 10)
    } else {
      // Non-authenticated users get limited general content (excluding Code Book)
      filteredBookReleases = (bookReleases || [])
        .filter(book => book.user_id !== codeBookUserId)
        .slice(0, 5)
      filteredDiaryEntries = (diaryEntries || [])
        .filter(entry => entry.user_id !== codeBookUserId)
        .slice(0, 10)
      filteredAudioPosts = (audioPosts || []).slice(0, 5)
    }

    // Combine and format posts
    const combinedPosts = [
      ...filteredDiaryEntries.map(entry => ({
        ...entry,
        type: 'diary' as const,
        isFollowing: user ? followingIds.has(entry.user.id) : false,
        isSubscribed: user ? subscribedIds.has(entry.user.id) : false,
        has_access: entry.is_free || (user ? subscribedIds.has(entry.user.id) : false) || (user && entry.user.id === user.id),
        reactions: diaryReactions[entry.id] || {},
        userReaction: userDiaryReactions[entry.id] || null
      })),
      ...filteredAudioPosts.map(post => ({
        ...post,
        type: 'audio' as const,
        isFollowing: user ? followingIds.has(post.user.id) : false,
        isSubscribed: user ? subscribedIds.has(post.user.id) : false,
        reactions: audioReactions[post.id] || {},
        userReaction: userAudioReactions[post.id] || null
      })),
      ...filteredBookReleases.map(book => ({
        ...book,
        type: 'book' as const,
        isFollowing: user ? followingIds.has(book.user.id) : false,
        isSubscribed: user ? subscribedIds.has(book.user.id) : false,
        reactions: bookReactions[book.id] || {},
        userReaction: userBookReactions[book.id] || null
      }))
    ]

    // Enhanced sorting: trending posts first, then chronological
    const { data: trendingData } = await supabase
      .rpc('get_trending_posts', {
        user_id_param: user?.id || null,
        limit_param: 50
      })

    // Create trending score map
    const trendingScores = new Map()
    if (trendingData) {
      trendingData.forEach((item: any) => {
        const key = `${item.content_type}-${item.content_id}`
        trendingScores.set(key, item.trending_score)
      })
    }

    // Sort by trending score first, then by created_at
    combinedPosts.sort((a, b) => {
      const aKey = `${a.type}-${a.id}`
      const bKey = `${b.type}-${b.id}`
      const aScore = trendingScores.get(aKey) || 0
      const bScore = trendingScores.get(bKey) || 0

      // If both have trending scores, sort by score
      if (aScore > 0 && bScore > 0) {
        return bScore - aScore
      }

      // If only one has a trending score, prioritize it
      if (aScore > 0 && bScore === 0) return -1
      if (bScore > 0 && aScore === 0) return 1

      // If neither has trending score, sort by date
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    })

    // Apply offset and limit to combined results
    const paginatedPosts = combinedPosts.slice(offset, offset + limit)

    return NextResponse.json({ 
      posts: paginatedPosts,
      hasMore: combinedPosts.length > offset + limit
    })
  } catch (error) {
    console.error('Timeline API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
