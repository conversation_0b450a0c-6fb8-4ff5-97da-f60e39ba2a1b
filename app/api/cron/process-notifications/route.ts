import { NextRequest, NextResponse } from 'next/server'
import { logCronAccess } from '@/lib/utils/security-logger'

// This endpoint can be called by a cron service like Vercel Cron or external cron
export async function GET(request: NextRequest) {
  try {
    // REQUIRED authentication for cron endpoints
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET_TOKEN

    // Require the secret token to be set
    if (!expectedToken) {
      console.error('CRON_SECRET_TOKEN environment variable not set')
      return NextResponse.json({
        error: 'Cron authentication not configured'
      }, { status: 500 })
    }

    // Validate the authorization header format
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({
        error: 'Missing or invalid authorization header. Expected: Bearer <token>'
      }, { status: 401 })
    }

    // Extract and validate the token
    const providedToken = authHeader.substring(7) // Remove 'Bearer ' prefix
    if (providedToken !== expectedToken) {
      // Log failed cron access attempt
      logCronAccess(request, '/api/cron/process-notifications', false, { reason: 'invalid_token' })

      // Add a small delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000))
      return NextResponse.json({ error: 'Invalid cron token' }, { status: 401 })
    }

    // Log successful cron access
    logCronAccess(request, '/api/cron/process-notifications', true)

    // Call the push notification processing endpoint
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const response = await fetch(`${baseUrl}/api/send-push-notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const result = await response.json()

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      ...result
    })

  } catch (error) {
    console.error('Error in cron notification processing:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process notifications',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Also allow POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}
