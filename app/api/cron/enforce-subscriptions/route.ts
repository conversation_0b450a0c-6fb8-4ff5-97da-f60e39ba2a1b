import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(request: NextRequest) {
  try {
    // REQUIRED authentication for cron endpoints
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET_TOKEN
    
    // Require the secret token to be set
    if (!expectedToken) {
      console.error('CRON_SECRET_TOKEN environment variable not set')
      return NextResponse.json({ 
        error: 'Cron authentication not configured' 
      }, { status: 500 })
    }
    
    // Validate the authorization header format
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ 
        error: 'Missing or invalid authorization header. Expected: Bearer <token>' 
      }, { status: 401 })
    }
    
    // Extract and validate the token
    const providedToken = authHeader.substring(7) // Remove 'Bearer ' prefix
    if (providedToken !== expectedToken) {
      // Add a small delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000))
      return NextResponse.json({ error: 'Invalid cron token' }, { status: 401 })
    }

    const supabase = await createSupabaseServerClient()
    
    console.log('Starting subscription enforcement check...')
    
    // 1. Find expired subscriptions that are still marked as active
    // Handle both old schema (active_until) and new schema (current_period_end)
    const { data: expiredSubs, error: expiredError } = await supabase
      .from('subscriptions')
      .select(`
        id,
        reader_id,
        writer_id,
        active_until,
        current_period_end,
        status,
        users!subscriptions_writer_id_fkey(name, email)
      `)
      .or(`current_period_end.lt.${new Date().toISOString()},active_until.lt.${new Date().toISOString()}`)
      .not('status', 'eq', 'expired')
    
    if (expiredError) {
      console.error('Error finding expired subscriptions:', expiredError)
      return NextResponse.json({ error: 'Failed to check subscriptions' }, { status: 500 })
    }

    let expiredCount = 0
    let errorCount = 0

    // 2. Mark expired subscriptions as expired
    if (expiredSubs && expiredSubs.length > 0) {
      console.log(`Found ${expiredSubs.length} expired subscriptions to process`)
      
      for (const sub of expiredSubs) {
        try {
          const { error: updateError } = await supabase
            .from('subscriptions')
            .update({ 
              status: 'expired',
              updated_at: new Date().toISOString()
            })
            .eq('id', sub.id)
          
          if (updateError) {
            console.error(`Failed to expire subscription ${sub.id}:`, updateError)
            errorCount++
          } else {
            console.log(`Expired subscription ${sub.id} for writer ${sub.users?.name}`)
            expiredCount++
          }
        } catch (error) {
          console.error(`Error processing subscription ${sub.id}:`, error)
          errorCount++
        }
      }
    }

    // 3. Clean up old expired subscriptions (optional - remove very old ones)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { error: cleanupError } = await supabase
      .from('subscriptions')
      .delete()
      .eq('status', 'expired')
      .or(`current_period_end.lt.${thirtyDaysAgo.toISOString()},active_until.lt.${thirtyDaysAgo.toISOString()}`)
    
    if (cleanupError) {
      console.warn('Failed to cleanup old subscriptions:', cleanupError)
    }

    // 4. Update subscription counts for writers (optional performance optimization)
    const { error: countError } = await supabase.rpc('update_all_subscriber_counts')
    
    if (countError) {
      console.warn('Failed to update subscriber counts:', countError)
    }

    const result = {
      success: true,
      timestamp: new Date().toISOString(),
      processed: expiredSubs?.length || 0,
      expired: expiredCount,
      errors: errorCount,
      message: `Processed ${expiredCount} expired subscriptions`
    }

    console.log('Subscription enforcement completed:', result)
    
    return NextResponse.json(result)

  } catch (error) {
    console.error('Error in subscription enforcement:', error)
    return NextResponse.json(
      { 
        error: 'Failed to enforce subscriptions',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Also allow POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request)
}
