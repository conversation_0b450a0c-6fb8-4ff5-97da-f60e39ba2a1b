import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Verify this is from Vercel Cron (optional but recommended)
    const authHeader = request.headers.get('authorization')

    // Only enforce auth in production, allow testing in development
    const isProduction = process.env.NODE_ENV === 'production'
    const hasValidAuth = authHeader === `Bearer ${process.env.CRON_SECRET}`

    if (isProduction && process.env.CRON_SECRET && !hasValidAuth) {
      console.log('Unauthorized cron request in production')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('Starting bestsellers update via Vercel cron...')

    // Call your Supabase function directly
    const response = await fetch('https://inzwekkutrwkcynvbyyu.supabase.co/functions/v1/update-daily-bestsellers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Supabase function failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    console.log('Bestsellers update completed:', data)

    return NextResponse.json({
      success: true,
      message: 'Bestsellers updated successfully via Vercel cron',
      supabaseResponse: data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in bestsellers cron:', error)
    
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Also support POST for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
