import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

// This endpoint is called automatically during deployment to create changelog entries
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get the request data
    const { commits, deployment_id, environment = 'production' } = await request.json()
    
    if (!commits || !Array.isArray(commits)) {
      return NextResponse.json(
        { error: 'Commits array is required' },
        { status: 400 }
      )
    }

    // Filter out commits that shouldn't generate changelog entries
    const filteredCommits = commits.filter(commit => {
      const message = commit.message?.toLowerCase() || ''
      
      // Skip these types of commits
      const skipPatterns = [
        'merge',
        'revert',
        'wip',
        'work in progress',
        'temp',
        'temporary',
        'test',
        'debug',
        'security',
        'secret',
        'password',
        'key',
        'token',
        'private',
        'internal',
        'admin only',
        'do not publish',
        'skip changelog',
        '[skip-changelog]',
        '[no-changelog]'
      ]
      
      return !skipPatterns.some(pattern => message.includes(pattern))
    })

    if (filteredCommits.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No commits to process for changelog',
        processed: 0
      })
    }

    const results = []
    let processedCount = 0

    // Process each commit
    for (const commit of filteredCommits) {
      try {
        // Call the database function to create changelog entry
        const { data, error } = await supabase.rpc('create_changelog_from_commit', {
          p_commit_hash: commit.hash || commit.id,
          p_commit_message: commit.message,
          p_commit_author: commit.author?.name || commit.author || 'OnlyDiary Team',
          p_commit_date: commit.timestamp || commit.date || new Date().toISOString()
        })

        if (error) {
          console.error('Error creating changelog entry:', error)
          results.push({
            commit: commit.hash || commit.id,
            success: false,
            error: error.message
          })
        } else if (data) {
          processedCount++
          results.push({
            commit: commit.hash || commit.id,
            success: true,
            entry_id: data
          })
        } else {
          // Commit was already processed (duplicate)
          results.push({
            commit: commit.hash || commit.id,
            success: true,
            skipped: 'already_processed'
          })
        }
      } catch (commitError) {
        console.error('Error processing commit:', commitError)
        results.push({
          commit: commit.hash || commit.id,
          success: false,
          error: commitError.message
        })
      }
    }

    // If we processed any commits, create a summary entry for multiple commits
    if (processedCount > 1) {
      const summaryTitle = `Daily Update - ${new Date().toLocaleDateString('en-US', { 
        month: 'long', 
        day: 'numeric', 
        year: 'numeric' 
      })}`
      
      const summaryContent = `# ${summaryTitle}

Hey everyone! 👋

Just pushed ${processedCount} updates to make OnlyDiary better:

## What Changed Today

${filteredCommits.map((commit, index) => 
  `${index + 1}. ${commit.message.split('\n')[0]}`
).join('\n')}

## Technical Details

- **Deployment**: ${deployment_id || 'Unknown'}
- **Environment**: ${environment}
- **Commits**: ${processedCount} changes
- **Date**: ${new Date().toLocaleDateString('en-US', { 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
  timeZoneName: 'short'
})}

All improvements are live now! No action needed on your part.

---

*Have feedback or suggestions? Feel free to reach out!*`

      // Create summary entry
      try {
        const { data: summaryData, error: summaryError } = await supabase.rpc('create_changelog_from_commit', {
          p_commit_hash: `summary-${deployment_id || Date.now()}`,
          p_commit_message: summaryContent,
          p_commit_author: 'OnlyDiary Team',
          p_commit_date: new Date().toISOString()
        })

        if (!summaryError && summaryData) {
          results.push({
            type: 'summary',
            success: true,
            entry_id: summaryData
          })
        }
      } catch (summaryError) {
        console.error('Error creating summary entry:', summaryError)
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${processedCount} commits for changelog`,
      processed: processedCount,
      total_commits: commits.length,
      filtered_commits: filteredCommits.length,
      results
    })

  } catch (error) {
    console.error('Error in auto-update changelog:', error)
    return NextResponse.json(
      { error: 'Failed to update changelog', details: error.message },
      { status: 500 }
    )
  }
}

// GET endpoint to check changelog status
export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get changelog user info
    const { data: changelogUser, error: userError } = await supabase
      .from('users')
      .select('id, name, created_at')
      .eq('name', 'OnlyDiary ChangeLog')
      .single()

    if (userError || !changelogUser) {
      return NextResponse.json({
        exists: false,
        message: 'ChangeLog user not found'
      })
    }

    // Get recent entries count
    const { count: entryCount } = await supabase
      .from('diary_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', changelogUser.id)
      .eq('is_hidden', false)

    // Get recent commits processed
    const { count: commitCount } = await supabase
      .from('changelog_commits')
      .select('*', { count: 'exact', head: true })
      .gte('processed_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

    return NextResponse.json({
      exists: true,
      user: changelogUser,
      stats: {
        total_entries: entryCount || 0,
        commits_this_week: commitCount || 0,
        last_updated: new Date().toISOString()
      },
      automation: {
        enabled: true,
        endpoint: '/api/changelog/auto-update',
        method: 'POST'
      }
    })

  } catch (error) {
    console.error('Error checking changelog status:', error)
    return NextResponse.json(
      { error: 'Failed to check changelog status' },
      { status: 500 }
    )
  }
}
