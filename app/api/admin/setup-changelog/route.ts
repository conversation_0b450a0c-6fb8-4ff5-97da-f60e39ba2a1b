import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get the current user and verify admin access
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Check if ChangeLog user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, name, email')
      .eq('name', 'OnlyDiary ChangeLog')
      .single()

    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: 'ChangeLog user already exists',
        user: existingUser
      })
    }

    // Create the ChangeLog user account
    const changelogUserData = {
      id: crypto.randomUUID(),
      email: '<EMAIL>',
      name: 'OnlyDiary ChangeLog',
      role: 'user' as const,
      bio: 'Official OnlyDiary development updates and feature announcements. Subscribe to stay updated on new features, improvements, and behind-the-scenes development insights.',
      avatar: '📝',
      price_monthly: 0, // Free changelog
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert(changelogUserData)
      .select()
      .single()

    if (createError) {
      console.error('Error creating changelog user:', createError)
      return NextResponse.json(
        { error: 'Failed to create changelog user', details: createError.message },
        { status: 500 }
      )
    }

    // Create a welcome changelog entry
    const welcomeEntry = {
      user_id: newUser.id,
      title: 'Welcome to the OnlyDiary ChangeLog! 🎉',
      body_md: `# Welcome to the OnlyDiary ChangeLog!

Hey everyone! 👋

This is the official OnlyDiary ChangeLog where I'll be sharing daily updates about new features, improvements, and behind-the-scenes development insights.

## What to Expect

**Daily Updates**: Every time I push a new version or make significant improvements, you'll see an update here. I'll write in plain language with just a touch of technical detail - enough to understand what's happening without getting lost in the weeds.

**Feature Announcements**: Be the first to know about new features before they're announced anywhere else.

**Development Insights**: Get a peek behind the curtain at how OnlyDiary is built and evolved.

**Community Feedback**: Your suggestions and feedback directly influence what gets built next.

## How It Works

- **Free Updates**: Most changelog entries will be free for everyone
- **Subscribe**: Hit the subscribe button to get these updates in your timeline
- **Timeline Integration**: Subscribed users will see changelog updates mixed in with their regular timeline

## Privacy & Security

Don't worry - I won't be sharing anything that could expose the platform to security risks or give competitors an unfair advantage. This is about transparency and keeping you informed, not about revealing trade secrets.

## Let's Build Together

OnlyDiary is built for you, and your feedback shapes every decision. Feel free to reach out with suggestions, bug reports, or just to say hi!

Ready to follow along on this journey? Hit that subscribe button! 🚀

---

*This changelog is updated daily with each deployment. Subscribe to never miss an update!*`,
      is_free: true,
      is_hidden: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { error: entryError } = await supabase
      .from('diary_entries')
      .insert(welcomeEntry)

    if (entryError) {
      console.error('Error creating welcome entry:', entryError)
      // Don't fail the whole operation if entry creation fails
    }

    return NextResponse.json({
      success: true,
      message: 'ChangeLog user created successfully',
      user: newUser,
      welcomeEntryCreated: !entryError
    })

  } catch (error) {
    console.error('Error setting up changelog:', error)
    return NextResponse.json(
      { error: 'Failed to setup changelog' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Check if ChangeLog user exists
    const { data: changelogUser, error } = await supabase
      .from('users')
      .select('id, name, email, bio, created_at')
      .eq('name', 'OnlyDiary ChangeLog')
      .single()

    if (error || !changelogUser) {
      return NextResponse.json({
        exists: false,
        message: 'ChangeLog user not found'
      })
    }

    // Get entry count
    const { count: entryCount } = await supabase
      .from('diary_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', changelogUser.id)
      .eq('is_hidden', false)

    return NextResponse.json({
      exists: true,
      user: changelogUser,
      entryCount: entryCount || 0
    })

  } catch (error) {
    console.error('Error checking changelog status:', error)
    return NextResponse.json(
      { error: 'Failed to check changelog status' },
      { status: 500 }
    )
  }
}
