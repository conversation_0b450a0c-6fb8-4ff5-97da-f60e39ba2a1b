import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { logSecurityEvent } from '@/lib/utils/security-logger'

/**
 * GET /api/admin/security-stats
 * Returns security statistics and monitoring data for admins
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Verify admin authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin') {
      logSecurityEvent('SUSPICIOUS_ACTIVITY', {
        request,
        userId: user.id,
        details: { reason: 'non_admin_accessing_security_stats' }
      })
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const daysBack = parseInt(searchParams.get('days') || '7')
    const validDaysBack = Math.min(Math.max(daysBack, 1), 90) // 1-90 days

    // Get security statistics
    const { data: stats, error: statsError } = await supabase
      .rpc('get_security_stats', { days_back: validDaysBack })
      .single()

    if (statsError) {
      console.error('Error fetching security stats:', statsError)
      return NextResponse.json({ error: 'Failed to fetch security statistics' }, { status: 500 })
    }

    // Get recent security events
    const { data: recentEvents, error: eventsError } = await supabase
      .from('security_events')
      .select(`
        id,
        event_type,
        user_id,
        user_email,
        ip_address,
        endpoint,
        severity,
        details,
        created_at
      `)
      .order('created_at', { ascending: false })
      .limit(50)

    if (eventsError) {
      console.error('Error fetching recent events:', eventsError)
      return NextResponse.json({ error: 'Failed to fetch recent events' }, { status: 500 })
    }

    // Get top rate limited IPs
    const { data: topViolators, error: violatorsError } = await supabase
      .from('rate_limit_violations')
      .select(`
        ip_address,
        endpoint,
        limit_type,
        violation_count,
        last_violation_at,
        user_id
      `)
      .gte('created_at', new Date(Date.now() - validDaysBack * 24 * 60 * 60 * 1000).toISOString())
      .order('violation_count', { ascending: false })
      .limit(20)

    if (violatorsError) {
      console.error('Error fetching rate limit violators:', violatorsError)
    }

    // Get file access statistics
    const { data: fileStats, error: fileStatsError } = await supabase
      .from('file_access_logs')
      .select('action, success, created_at')
      .gte('created_at', new Date(Date.now() - validDaysBack * 24 * 60 * 60 * 1000).toISOString())

    if (fileStatsError) {
      console.error('Error fetching file stats:', fileStatsError)
    }

    // Process file statistics
    const fileStatsSummary = fileStats?.reduce((acc, log) => {
      const action = log.action
      if (!acc[action]) {
        acc[action] = { total: 0, successful: 0, failed: 0 }
      }
      acc[action].total++
      if (log.success) {
        acc[action].successful++
      } else {
        acc[action].failed++
      }
      return acc
    }, {} as Record<string, { total: number; successful: number; failed: number }>)

    // Log admin access to security stats
    logSecurityEvent('ADMIN_LOGIN', {
      request,
      userId: user.id,
      details: { action: 'view_security_stats', daysBack: validDaysBack }
    })

    // Return comprehensive security dashboard data
    return NextResponse.json({
      success: true,
      period: {
        days: validDaysBack,
        startDate: new Date(Date.now() - validDaysBack * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString()
      },
      overview: {
        totalFileAccesses: stats?.total_file_accesses || 0,
        failedFileAccesses: stats?.failed_file_accesses || 0,
        securityEventsBySeverity: stats?.security_events_by_severity || {},
        uniqueIpsWithViolations: stats?.unique_ips_with_violations || 0,
        topViolatedEndpoints: stats?.top_violated_endpoints || []
      },
      fileActivity: fileStatsSummary || {},
      recentSecurityEvents: recentEvents || [],
      topRateLimitViolators: topViolators || [],
      alerts: generateSecurityAlerts(stats, topViolators, recentEvents)
    })

  } catch (error) {
    console.error('Error in security stats endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Generates security alerts based on the data
 */
function generateSecurityAlerts(
  stats: any,
  violators: any[],
  events: any[]
): Array<{ type: string; severity: string; message: string; count?: number }> {
  const alerts = []

  // Check for high rate limit violations
  const highViolationIPs = violators?.filter(v => v.violation_count > 50) || []
  if (highViolationIPs.length > 0) {
    alerts.push({
      type: 'HIGH_RATE_LIMIT_VIOLATIONS',
      severity: 'HIGH',
      message: `${highViolationIPs.length} IP(s) with excessive rate limit violations (>50)`,
      count: highViolationIPs.length
    })
  }

  // Check for critical security events
  const criticalEvents = events?.filter(e => e.severity === 'CRITICAL') || []
  if (criticalEvents.length > 0) {
    alerts.push({
      type: 'CRITICAL_SECURITY_EVENTS',
      severity: 'CRITICAL',
      message: `${criticalEvents.length} critical security event(s) detected`,
      count: criticalEvents.length
    })
  }

  // Check for failed file access rate
  const totalFileAccesses = stats?.total_file_accesses || 0
  const failedFileAccesses = stats?.failed_file_accesses || 0
  if (totalFileAccesses > 0) {
    const failureRate = (failedFileAccesses / totalFileAccesses) * 100
    if (failureRate > 10) { // More than 10% failure rate
      alerts.push({
        type: 'HIGH_FILE_ACCESS_FAILURE_RATE',
        severity: 'MEDIUM',
        message: `High file access failure rate: ${failureRate.toFixed(1)}%`,
        count: failedFileAccesses
      })
    }
  }

  // Check for admin login failures
  const adminLoginFailures = events?.filter(e => e.event_type === 'ADMIN_LOGIN_FAILED') || []
  if (adminLoginFailures.length > 3) {
    alerts.push({
      type: 'MULTIPLE_ADMIN_LOGIN_FAILURES',
      severity: 'HIGH',
      message: `${adminLoginFailures.length} failed admin login attempts`,
      count: adminLoginFailures.length
    })
  }

  return alerts
}
