import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { compare } from 'bcryptjs'
import { checkRateLimit, createRateLimitResponse, createRateLimitHeaders } from '@/lib/utils/rate-limit'
import { logAdminAuthEvent, logRateLimitEvent } from '@/lib/utils/security-logger'

// Require admin password to be set in environment variables
const ADMIN_PASSWORD_HASH = process.env.ADMIN_PASSWORD_HASH

export async function POST(request: NextRequest) {
  try {
    // Apply strict rate limiting for admin authentication
    const rateLimitResult = checkRateLimit(request, 'AUTH', 'admin-login')

    if (!rateLimitResult.allowed) {
      logRateLimitEvent(request, 'AUTH', { endpoint: 'admin-login' })
      return createRateLimitResponse('AUTH', rateLimitResult)
    }
    const { password } = await request.json()

    // Ensure admin password is configured
    if (!ADMIN_PASSWORD_HASH) {
      console.error('ADMIN_PASSWORD_HASH environment variable not set')
      return NextResponse.json(
        { error: 'Admin authentication not configured' },
        { status: 500 }
      )
    }

    // Validate password input
    if (!password || typeof password !== 'string' || password.length < 8) {
      return NextResponse.json(
        { error: 'Invalid password format' },
        { status: 400 }
      )
    }

    // Check password using bcrypt
    const isValidPassword = await compare(password, ADMIN_PASSWORD_HASH)
    if (!isValidPassword) {
      // Log failed admin authentication attempt
      logAdminAuthEvent(false, request, { reason: 'invalid_password' })

      // Add a small delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000))
      return NextResponse.json(
        { error: 'Invalid password' },
        { status: 401 }
      )
    }

    // Verify user is actually an admin in the database
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Must be logged in' },
        { status: 401 }
      )
    }

    // Check if user has admin role
    const { data: profile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin role required' },
        { status: 403 }
      )
    }

    // Log successful admin authentication
    logAdminAuthEvent(true, request, { userId: profile.id })

    // Return success response with rate limit headers
    const rateLimitHeaders = createRateLimitHeaders(rateLimitResult)

    return NextResponse.json(
      { success: true },
      { headers: rateLimitHeaders }
    )

  } catch (error) {
    console.error('Admin auth error:', error)
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    )
  }
}
