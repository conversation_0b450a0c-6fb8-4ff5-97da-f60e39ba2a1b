import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user and verify admin access
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single()

    if (userProfile?.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    console.log('Starting manual bestsellers update...')

    // Call the database function to update bestsellers
    const { data, error } = await supabase.rpc('update_daily_bestsellers')

    if (error) {
      console.error('Error updating bestsellers:', error)
      return NextResponse.json(
        { error: 'Failed to update bestsellers', details: error.message },
        { status: 500 }
      )
    }

    // Get counts for verification
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    const { data: freeBestsellers, error: freeError } = await supabase
      .from('daily_bestsellers')
      .select('*')
      .eq('date', yesterday)
      .eq('book_type', 'free')

    const { data: paidBestsellers, error: paidError } = await supabase
      .from('daily_bestsellers')
      .select('*')
      .eq('date', yesterday)
      .eq('book_type', 'paid')

    const result = {
      success: true,
      message: 'Bestsellers updated successfully',
      date: yesterday,
      counts: {
        free_bestsellers: freeBestsellers?.length || 0,
        paid_bestsellers: paidBestsellers?.length || 0
      },
      timestamp: new Date().toISOString()
    }

    console.log('Bestsellers update result:', result)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Error in manual bestsellers update:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
