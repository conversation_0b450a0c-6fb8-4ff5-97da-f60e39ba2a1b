import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const { writerId, credits = 30 } = await request.json()

    if (!writerId) {
      return NextResponse.json(
        { error: "Writer ID is required" },
        { status: 400 }
      )
    }

    // Validate writer exists
    const { data: writer, error: writerError } = await supabase
      .from("users")
      .select("id, name, price_monthly")
      .eq("id", writerId)
      .in("role", ["user", "admin"]) // In unified system, all users can receive credits
      .single()

    if (writerError || !writer) {
      return NextResponse.json(
        { error: "Writer not found" },
        { status: 404 }
      )
    }

    // Calculate price (default $9.99 for 30 credits)
    const pricePerCredit = Math.floor((writer.price_monthly || 999) / 30) // Convert monthly to per-post
    const totalPrice = pricePerCredit * credits

    // TODO: Integrate with Stripe for actual payment processing
    // For now, we'll simulate a successful payment
    const mockStripePaymentId = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create payment record
    const { data: payment, error: paymentError } = await supabase
      .from("payments")
      .insert({
        payer_id: user.id,
        writer_id: writerId,
        kind: 'sub', // Using 'sub' for credit purchases
        amount_cents: totalPrice,
        stripe_payment_id: mockStripePaymentId,
        credits_purchased: credits
      })
      .select()
      .single()

    if (paymentError) {
      return NextResponse.json(
        { error: "Failed to record payment" },
        { status: 500 }
      )
    }

    // Add credits to user's account
    const { data: existingCredits } = await supabase
      .from("post_credits")
      .select("*")
      .eq("user_id", user.id)
      .eq("writer_id", writerId)
      .single()

    if (existingCredits) {
      // Update existing credits
      const { error: updateError } = await supabase
        .from("post_credits")
        .update({
          credits_remaining: existingCredits.credits_remaining + credits,
          total_credits_purchased: existingCredits.total_credits_purchased + credits,
          last_purchase_date: new Date().toISOString()
        })
        .eq("user_id", user.id)
        .eq("writer_id", writerId)

      if (updateError) {
        return NextResponse.json(
          { error: "Failed to update credits" },
          { status: 500 }
        )
      }
    } else {
      // Create new credit record
      const { error: insertError } = await supabase
        .from("post_credits")
        .insert({
          user_id: user.id,
          writer_id: writerId,
          credits_remaining: credits,
          total_credits_purchased: credits,
          last_purchase_date: new Date().toISOString()
        })

      if (insertError) {
        return NextResponse.json(
          { error: "Failed to create credits" },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully purchased ${credits} post credits for ${writer.name}`,
      credits_purchased: credits,
      total_paid: totalPrice,
      payment_id: payment.id
    })

  } catch (error) {
    console.error("Error purchasing credits:", error)
    return NextResponse.json(
      { error: "Failed to purchase credits" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Credit Purchase API',
    version: '1.0.0',
    pricing: {
      default_credits: 30,
      default_price: '$9.99',
      description: 'Purchase credits to read posts from your favorite writers'
    }
  })
}
