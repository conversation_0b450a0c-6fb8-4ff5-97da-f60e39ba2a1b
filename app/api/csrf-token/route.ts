import { NextRequest, NextResponse } from 'next/server'
import { generateCSRFToken, createCSRFHeaders } from '@/lib/utils/csrf'

/**
 * GET /api/csrf-token
 * Generates and returns a CSRF token for client-side use
 */
export async function GET(request: NextRequest) {
  try {
    // Generate CSRF token and secret
    const { token, secret } = generateCSRFToken()
    
    // Create response with CSRF headers
    const headers = createCSRFHeaders(token, secret)
    
    // Return token to client (secret is in HttpOnly cookie)
    return NextResponse.json(
      { 
        csrfToken: token,
        message: 'CSRF token generated successfully'
      },
      { 
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      }
    )
    
  } catch (error) {
    console.error('Error generating CSRF token:', error)
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}
