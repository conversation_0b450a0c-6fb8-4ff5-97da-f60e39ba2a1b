import { NextRequest, NextResponse } from 'next/server'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { r2Client, AUDIO_BUCKET, generateAudioKey, getAudioUrl } from '@/lib/cloudflare-r2'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Audio upload API called')

    // Verify user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    console.log('Auth check:', { user: !!user, authError })

    if (authError || !user) {
      console.log('Authentication failed:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Request body:', body)

    const { type, duration, fileSize } = body

    // Validate input types
    if (!type || !['post', 'reply'].includes(type)) {
      console.log('Invalid type:', type)
      return NextResponse.json({ error: 'Invalid type. Must be "post" or "reply"' }, { status: 400 })
    }

    // Validate duration based on type
    const maxDuration = type === 'post' ? 9.2 : 40.2; // OnlyAudio posts: 9s, Book discussions: 40s
    if (!duration || typeof duration !== 'number' || duration <= 0 || duration > maxDuration) {
      console.log('Invalid duration:', duration)
      const maxDisplay = type === 'post' ? '9 seconds' : '40 seconds';
      return NextResponse.json({
        error: `Duration must be between 0.1 and ${maxDisplay}`
      }, { status: 400 })
    }

    // Validate file size if provided (max 5MB for audio)
    const MAX_AUDIO_SIZE = 5 * 1024 * 1024; // 5MB
    if (fileSize && (typeof fileSize !== 'number' || fileSize > MAX_AUDIO_SIZE)) {
      return NextResponse.json({ error: 'Audio file must be less than 5MB' }, { status: 400 })
    }

    if (fileSize && fileSize < 100) { // Minimum 100 bytes
      return NextResponse.json({ error: 'Audio file appears to be empty or corrupted' }, { status: 400 })
    }

    console.log('Generating audio key for user:', user.id)

    // Generate unique key for the audio file
    const key = generateAudioKey(user.id, type)
    console.log('Generated key:', key)

    // Create presigned URL for upload with enhanced security
    const command = new PutObjectCommand({
      Bucket: AUDIO_BUCKET,
      Key: key,
      ContentType: 'audio/webm', // Restrict to WebM audio format
      ContentLength: fileSize || undefined,
      Metadata: {
        userId: user.id,
        type: type,
        duration: duration.toString(),
        uploadedAt: new Date().toISOString(),
        fileSize: fileSize?.toString() || 'unknown',
        validated: 'true',
        maxDuration: maxDuration.toString()
      },
    })

    console.log('Creating presigned URL...')
    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }) // 1 hour
    const publicUrl = getAudioUrl(key)

    console.log('Upload URL created successfully')

    return NextResponse.json({
      uploadUrl,
      key,
      publicUrl
    })
  } catch (error) {
    console.error('Audio upload error:', error)
    return NextResponse.json({ error: 'Upload failed', details: error.message }, { status: 500 })
  }
}
