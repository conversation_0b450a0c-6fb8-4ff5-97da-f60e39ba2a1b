import { createServerSupabaseClient } from "@/lib/supabase/server"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  request: NextRequest,
  { params }: { params: { bookId: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get book details
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        meta_description,
        user_id,
        is_ebook,
        processing_status,
        total_chapters,
        created_at
      `)
      .eq('id', params.bookId)
      .single()

    if (bookError) {
      return NextResponse.json({ error: 'Book not found', details: bookError }, { status: 404 })
    }

    // Get chapters
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select(`
        id,
        title,
        content,
        chapter_number,
        word_count,
        is_published,
        created_at
      `)
      .eq('project_id', params.bookId)
      .order('chapter_number')

    if (chaptersError) {
      return NextResponse.json({ error: 'Failed to fetch chapters', details: chaptersError }, { status: 500 })
    }

    // Check processing jobs
    const { data: processingJobs, error: jobsError } = await supabase
      .from('book_processing_jobs')
      .select('*')
      .eq('book_id', params.bookId)
      .order('created_at', { ascending: false })

    return NextResponse.json({
      book: {
        ...book,
        description_preview: book.meta_description?.substring(0, 200) + '...'
      },
      chapters: chapters?.map(chapter => ({
        ...chapter,
        content_preview: chapter.content?.substring(0, 200) + '...',
        content_length: chapter.content?.length || 0,
        is_description_match: chapter.content === book.meta_description
      })) || [],
      processing_jobs: processingJobs || [],
      debug_info: {
        total_chapters_in_db: chapters?.length || 0,
        book_says_total_chapters: book.total_chapters,
        chapters_published: chapters?.filter(c => c.is_published).length || 0,
        first_chapter_content_starts_with: chapters?.[0]?.content?.substring(0, 100) || 'No content',
        book_description_starts_with: book.meta_description?.substring(0, 100) || 'No description'
      }
    })

  } catch (error) {
    console.error('Debug API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
