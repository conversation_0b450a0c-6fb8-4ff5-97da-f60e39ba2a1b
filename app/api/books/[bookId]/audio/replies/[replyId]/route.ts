import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkBookReadAccess } from '@/lib/utils/book-access'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string; replyId: string }> }
) {
  try {
    const { bookId, replyId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Verify the reply exists and belongs to the user
    const { data: reply, error: replyError } = await supabase
      .from('book_audio_replies')
      .select(`
        id, 
        user_id, 
        book_audio_post_id,
        book_audio_posts!inner(project_id)
      `)
      .eq('id', replyId)
      .single()

    if (replyError || !reply) {
      return NextResponse.json({ error: 'Reply not found' }, { status: 404 })
    }

    // Verify the reply belongs to the correct book
    if (reply.book_audio_posts.project_id !== bookId) {
      return NextResponse.json({ error: 'Reply does not belong to this book' }, { status: 403 })
    }

    // Check if the user owns this reply
    if (reply.user_id !== user.id) {
      return NextResponse.json({ error: 'You can only delete your own replies' }, { status: 403 })
    }

    // Delete all nested replies to this reply first
    const { error: nestedRepliesError } = await supabase
      .from('book_audio_replies')
      .delete()
      .eq('parent_reply_id', replyId)

    if (nestedRepliesError) {
      console.error('Error deleting nested replies:', nestedRepliesError)
      return NextResponse.json({ error: 'Failed to delete nested replies' }, { status: 500 })
    }

    // Delete all reactions to this reply
    const { error: reactionsError } = await supabase
      .from('reactions')
      .delete()
      .eq('book_audio_reply_id', replyId)

    if (reactionsError) {
      console.error('Error deleting reactions:', reactionsError)
      return NextResponse.json({ error: 'Failed to delete reactions' }, { status: 500 })
    }

    // Delete the reply itself
    const { error: deleteError } = await supabase
      .from('book_audio_replies')
      .delete()
      .eq('id', replyId)
      .eq('user_id', user.id) // Double-check ownership

    if (deleteError) {
      console.error('Error deleting reply:', deleteError)
      return NextResponse.json({ error: 'Failed to delete reply' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete reply API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
