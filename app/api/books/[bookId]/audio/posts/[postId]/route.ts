import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkBookReadAccess } from '@/lib/utils/book-access'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ bookId: string; postId: string }> }
) {
  try {
    const { bookId, postId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has access to this book
    const accessResult = await checkBookReadAccess(supabase, user.id, bookId)
    if (!accessResult.hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Verify the post exists and belongs to the user
    const { data: post, error: postError } = await supabase
      .from('book_audio_posts')
      .select('id, user_id, project_id')
      .eq('id', postId)
      .eq('project_id', bookId)
      .single()

    if (postError || !post) {
      return NextResponse.json({ error: 'Post not found' }, { status: 404 })
    }

    // Check if the user owns this post
    if (post.user_id !== user.id) {
      return NextResponse.json({ error: 'You can only delete your own posts' }, { status: 403 })
    }

    // Delete all replies to this post first
    const { error: repliesError } = await supabase
      .from('book_audio_replies')
      .delete()
      .eq('book_audio_post_id', postId)

    if (repliesError) {
      console.error('Error deleting replies:', repliesError)
      return NextResponse.json({ error: 'Failed to delete replies' }, { status: 500 })
    }

    // Delete all reactions to this post
    const { error: reactionsError } = await supabase
      .from('reactions')
      .delete()
      .eq('book_audio_post_id', postId)

    if (reactionsError) {
      console.error('Error deleting reactions:', reactionsError)
      return NextResponse.json({ error: 'Failed to delete reactions' }, { status: 500 })
    }

    // Delete the post itself
    const { error: deleteError } = await supabase
      .from('book_audio_posts')
      .delete()
      .eq('id', postId)
      .eq('user_id', user.id) // Double-check ownership

    if (deleteError) {
      console.error('Error deleting post:', deleteError)
      return NextResponse.json({ error: 'Failed to delete post' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete post API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
