import { createServerSupabaseClient } from "@/lib/supabase/server"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get processing job status
    const { data: job, error: jobError } = await supabase
      .from('book_processing_jobs')
      .select(`
        *,
        projects!inner(
          id,
          title,
          user_id
        )
      `)
      .eq('id', params.jobId)
      .single()

    if (jobError) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    // Check if user owns this book
    if (job.projects.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Calculate progress percentage
    let progress = 0
    if (job.total_chapters > 0) {
      progress = Math.round((job.processed_chapters / job.total_chapters) * 100)
    }

    // Determine current step message
    let currentStep = 'Processing...'
    switch (job.status) {
      case 'pending':
        currentStep = 'Queued for processing...'
        progress = 0
        break
      case 'extracting':
        currentStep = 'Extracting text content...'
        progress = Math.max(10, progress)
        break
      case 'structuring':
        currentStep = `Creating chapters... (${job.processed_chapters}/${job.total_chapters})`
        progress = Math.max(20, progress)
        break
      case 'indexing':
        currentStep = `Indexing for highlights... (${job.processed_chapters}/${job.total_chapters})`
        progress = Math.max(70, progress)
        break
      case 'optimizing':
        currentStep = 'Optimizing for fast reading...'
        progress = 95
        break
      case 'completed':
        currentStep = 'Book ready for reading!'
        progress = 100
        break
      case 'failed':
        currentStep = 'Processing failed'
        progress = 0
        break
    }

    return NextResponse.json({
      status: job.status,
      progress,
      currentStep,
      totalChapters: job.total_chapters,
      processedChapters: job.processed_chapters,
      error: job.error_message,
      bookId: job.book_id,
      bookTitle: job.projects.title,
      startedAt: job.started_at,
      completedAt: job.completed_at
    })

  } catch (error) {
    console.error('Error fetching processing status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Optional: Allow canceling processing jobs
export async function DELETE(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const supabase = createSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get processing job
    const { data: job, error: jobError } = await supabase
      .from('book_processing_jobs')
      .select(`
        *,
        projects!inner(user_id)
      `)
      .eq('id', params.jobId)
      .single()

    if (jobError) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 })
    }

    // Check if user owns this book
    if (job.projects.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Only allow canceling if not completed
    if (job.status === 'completed') {
      return NextResponse.json({ error: 'Cannot cancel completed job' }, { status: 400 })
    }

    // Mark job as failed/cancelled
    const { error: updateError } = await supabase
      .from('book_processing_jobs')
      .update({
        status: 'failed',
        error_message: 'Cancelled by user',
        completed_at: new Date().toISOString()
      })
      .eq('id', params.jobId)

    if (updateError) {
      throw updateError
    }

    // Also update the book status
    await supabase
      .from('projects')
      .update({ processing_status: 'failed' })
      .eq('id', job.book_id)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error canceling processing job:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
