import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { bookId } = await request.json()
    
    if (!bookId) {
      return NextResponse.json(
        { error: 'Missing book ID' },
        { status: 400 }
      )
    }

    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get book details and verify it's free
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        price_amount,
        user_id,
        users!inner(name)
      `)
      .eq('id', bookId)
      .eq('is_ebook', true)
      .single()

    if (bookError || !book) {
      return NextResponse.json(
        { error: 'Book not found' },
        { status: 404 }
      )
    }

    if (book.price_amount > 0) {
      return NextResponse.json(
        { error: 'This book is not free' },
        { status: 400 }
      )
    }

    // Check if user has already "purchased" this free book
    const { data: existingPurchase } = await supabase
      .from('book_purchases')
      .select('id')
      .eq('user_id', user.id)
      .eq('project_id', bookId)
      .single()

    if (existingPurchase) {
      return NextResponse.json(
        { message: 'Book already in your library', success: true }
      )
    }

    // Add to user's library
    const { error: libraryError } = await supabase
      .from('user_library')
      .insert({
        user_id: user.id,
        project_id: bookId,
        access_type: 'free'
      })

    if (libraryError) {
      console.error('Error adding to library:', libraryError)
      return NextResponse.json(
        { error: 'Failed to add book to library' },
        { status: 500 }
      )
    }

    // Create a book_purchases entry for sales tracking (with 0 price)
    // Use upsert to handle duplicate attempts gracefully
    const { error: purchaseError } = await supabase
      .from('book_purchases')
      .upsert({
        user_id: user.id,
        project_id: bookId,
        purchase_price_cents: 0
        // Note: No status column in the actual table structure
      }, {
        onConflict: 'user_id,project_id'
      })

    if (purchaseError) {
      console.error('Error creating purchase record:', purchaseError)
      // Don't fail the request if this fails, library entry is more important
    }

    return NextResponse.json({ 
      message: 'Book added to your library successfully!',
      success: true 
    })

  } catch (error) {
    console.error('Error processing free download:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
