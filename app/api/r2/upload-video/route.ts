import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import r2Client from "@/lib/r2-client";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { checkRateLimit, createRateLimitResponse, createRateLimitHeaders } from '@/lib/utils/rate-limit'
import { logFileEvent, logRateLimitEvent } from '@/lib/utils/security-logger'

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting for video uploads
    const rateLimitResult = checkRateLimit(request, 'UPLOAD', 'video-upload')

    if (!rateLimitResult.allowed) {
      logRateLimitEvent(request, 'UPLOAD', { endpoint: 'video-upload' })
      return createRateLimitResponse('UPLOAD', rateLimitResult)
    }
    const { filename, postId, fileSize, mimeType } = await request.json();

    // Input validation
    if (!filename || !postId) {
      return NextResponse.json(
        { error: "Filename and postId are required" },
        { status: 400 }
      );
    }

    // Validate filename format (prevent path traversal)
    if (typeof filename !== 'string' || filename.length > 255) {
      return NextResponse.json(
        { error: "Invalid filename format" },
        { status: 400 }
      );
    }

    // Sanitize filename - remove dangerous characters
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');

    // Validate file extension
    const allowedExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
    const fileExtension = sanitizedFilename.toLowerCase().substring(sanitizedFilename.lastIndexOf('.'));

    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { error: "Only MP4, MOV, AVI, MKV, and WebM files are allowed" },
        { status: 400 }
      );
    }

    // Validate MIME type if provided
    const allowedMimeTypes = [
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/x-matroska',
      'video/webm'
    ];

    if (mimeType && !allowedMimeTypes.includes(mimeType)) {
      return NextResponse.json(
        { error: "Invalid file type. Only video files are allowed." },
        { status: 400 }
      );
    }

    // Validate file size (max 500MB)
    const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB in bytes
    if (fileSize && fileSize > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size must be less than 500MB" },
        { status: 400 }
      );
    }

    if (fileSize && fileSize < 1024) { // Minimum 1KB
      return NextResponse.json(
        { error: "File appears to be empty or corrupted" },
        { status: 400 }
      );
    }

    // Check R2 configuration
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
      console.error("Missing Cloudflare R2 environment variables");
      return NextResponse.json(
        { error: "R2 configuration missing" },
        { status: 500 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Create unique file path with additional randomness for security
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileKey = `videos/${user.id}/${timestamp}-${randomString}-${sanitizedFilename}`;

    // Determine content type based on file extension or provided MIME type
    const getContentType = (extension: string, providedMimeType?: string): string => {
      if (providedMimeType && allowedMimeTypes.includes(providedMimeType)) {
        return providedMimeType;
      }

      const contentTypeMap: Record<string, string> = {
        '.mp4': 'video/mp4',
        '.mov': 'video/quicktime',
        '.avi': 'video/x-msvideo',
        '.mkv': 'video/x-matroska',
        '.webm': 'video/webm'
      };

      return contentTypeMap[extension] || 'video/mp4';
    };

    const contentType = getContentType(fileExtension, mimeType);

    // Generate presigned URL for direct client upload with enhanced security
    const command = new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Key: fileKey,
      ContentType: contentType,
      ContentLength: fileSize || undefined,
      Metadata: {
        'original-filename': filename,
        'upload-user-id': user.id,
        'upload-timestamp': new Date().toISOString(),
        'file-size': fileSize?.toString() || 'unknown',
        'validated': 'true'
      }
    });

    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }); // 1 hour
    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${fileKey}`;

    // Get the diary entry's is_free status to inherit
    const { data: diaryEntry } = await supabase
      .from("diary_entries")
      .select("is_free")
      .eq("id", postId)
      .single();

    // Create video record in database
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        title: filename.replace(/\.[^/.]+$/, ""), // Remove file extension
        r2_file_key: fileKey,
        r2_public_url: publicUrl,
        is_free: diaryEntry?.is_free || false // Inherit from diary entry
      })
      .select()
      .single();

    if (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      );
    }

    // Log successful video upload preparation
    logFileEvent('upload', request, user.id, fileKey, {
      filename: sanitizedFilename,
      fileSize,
      mimeType,
      postId
    })

    // Return success response with rate limit headers
    const rateLimitHeaders = createRateLimitHeaders(rateLimitResult)

    return NextResponse.json({
      uploadUrl,
      publicUrl,
      video
    }, {
      headers: rateLimitHeaders
    });

  } catch (error) {
    console.error("Error generating upload URL:", error);
    return NextResponse.json(
      { error: "Failed to generate upload URL" },
      { status: 500 }
    );
  }
}
