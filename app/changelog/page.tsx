import { createServerSupabaseClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { SubscribeButton } from "@/components/SubscribeButton"
import { SmartTypography } from "@/components/SmartTypography"

interface ChangeLogEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  love_count: number
  view_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

// Custom time formatting function to avoid date-fns dependency
function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'just now'
  if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`

  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`

  const diffInYears = Math.floor(diffInMonths / 12)
  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`
}

export default async function ChangeLogPage() {
  const supabase = await createServerSupabaseClient()
  
  // Get the current user (if any)
  const { data: { user } } = await supabase.auth.getUser()
  
  // Find the OnlyDiary ChangeLog user
  const { data: changelogUser, error: userError } = await supabase
    .from('users')
    .select('id, name, avatar, profile_picture_url, bio, price_monthly')
    .eq('name', 'OnlyDiary ChangeLog')
    .single()

  if (userError || !changelogUser) {
    console.error('ChangeLog user not found:', userError)
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-gray-800 mb-4">ChangeLog Coming Soon</h1>
          <p className="text-gray-600">The OnlyDiary ChangeLog is being set up. Check back soon!</p>
          <Link href="/" className="inline-block mt-4">
            <Button variant="outline">← Back to Home</Button>
          </Link>
        </div>
      </div>
    )
  }

  // Check if user is subscribed to changelog
  let isSubscribed = false
  let hasAccess = true // Default to true for free entries
  
  if (user) {
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('id, active_until')
      .eq('subscriber_id', user.id)
      .eq('writer_id', changelogUser.id)
      .gte('active_until', new Date().toISOString())
      .single()
    
    isSubscribed = !!subscription
  }

  // Get changelog entries
  const { data: entries, error: entriesError } = await supabase
    .from('diary_entries')
    .select(`
      id,
      title,
      body_md,
      created_at,
      is_free,
      love_count,
      view_count,
      user:users!user_id (
        id,
        name,
        avatar,
        profile_picture_url
      )
    `)
    .eq('user_id', changelogUser.id)
    .eq('is_hidden', false)
    .order('created_at', { ascending: false })
    .limit(20)

  if (entriesError) {
    console.error('Error fetching changelog entries:', entriesError)
  }

  const changelogEntries: ChangeLogEntry[] = entries || []

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              📝
            </div>
            <div>
              <h1 className="text-3xl font-serif text-gray-800 mb-2">The Code Book</h1>
              <p className="text-gray-600 font-serif">
                Daily updates on new features, improvements, and behind-the-scenes development
              </p>
            </div>
          </div>

          {/* Subscription Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Stay Updated on OnlyDiary's Evolution
                </h3>
                <p className="text-gray-600 text-sm">
                  Subscribe to get Code Book updates in your timeline and never miss a feature release.
                  {changelogUser.price_monthly && changelogUser.price_monthly > 0 ? (
                    ` Premium updates for $${(changelogUser.price_monthly / 100).toFixed(2)}/month.`
                  ) : (
                    " Free updates delivered to your timeline."
                  )}
                </p>
              </div>
              
              <div className="flex-shrink-0">
                {user ? (
                  isSubscribed ? (
                    <div className="flex items-center gap-2 text-green-600">
                      <span className="text-sm font-medium">✓ Subscribed</span>
                    </div>
                  ) : (
                    <SubscribeButton writerId={changelogUser.id} />
                  )
                ) : (
                  <Link href="/login">
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Login to Subscribe
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Changelog Entries */}
        <div className="space-y-6">
          {changelogEntries.length > 0 ? (
            changelogEntries.map((entry) => {
              // For changelog, we'll show full access to free entries
              // and teasers for paid entries if not subscribed
              const canReadFull = entry.is_free || isSubscribed || !user

              return (
                <ChangeLogEntryCard
                  key={entry.id}
                  entry={entry}
                  canReadFull={canReadFull}
                  isSubscribed={isSubscribed}
                />
              )
            })
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                First Code Book Entry Coming Soon
              </h3>
              <p className="text-gray-600">
                We're preparing the first Code Book entry. Subscribe to be notified when it's published!
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="text-center">
            <p className="text-gray-500 text-sm">
              Want to suggest a feature or report a bug?{" "}
              <Link href="/messages" className="text-blue-600 hover:text-blue-700 underline">
                Send us a message
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// ChangeLog Entry Card Component
function ChangeLogEntryCard({
  entry,
  canReadFull,
  isSubscribed
}: {
  entry: ChangeLogEntry
  canReadFull: boolean
  isSubscribed: boolean
}) {
  // Get preview text (first 200 characters for changelog)
  const cleanText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .trim()

  const previewText = cleanText.length > 200
    ? cleanText.substring(0, 200) + '...'
    : cleanText

  const fullText = entry.body_md

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white text-lg">
            📝
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-900">The Code Book</span>
              <span className="text-gray-400">•</span>
              <span className="text-sm text-gray-500">
                {formatTimeAgo(entry.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Title */}
        <h2 className="text-xl font-serif text-gray-800 mb-3 line-clamp-2">
          {entry.title}
        </h2>

        {/* Content */}
        <div className="prose prose-sm max-w-none">
          {canReadFull ? (
            <SmartTypography content={fullText} />
          ) : (
            <div>
              <p className="text-gray-600 mb-4">{previewText}</p>
              {!isSubscribed && (
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm font-medium mb-2">
                    🔒 Subscribe to read the full Code Book entry
                  </p>
                  <p className="text-blue-600 text-sm">
                    Get all development updates and behind-the-scenes insights delivered to your timeline.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>👀 {entry.view_count || 0} views</span>
            <span>❤️ {entry.love_count || 0} hearts</span>
            {entry.is_free && (
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                Free
              </span>
            )}
          </div>

          <Link
            href={`/d/${entry.id}`}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Read full entry →
          </Link>
        </div>
      </div>
    </div>
  )
}
