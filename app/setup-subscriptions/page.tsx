'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function SetupSubscriptionsPage() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const router = useRouter()
  const supabase = createSupabaseClient()

  const createTestSubscriptions = async () => {
    setLoading(true)
    setMessage('')
    setError('')

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setError('Not authenticated')
        setLoading(false)
        return
      }

      // Find writers with published entries
      const { data: writers, error: writersError } = await supabase
        .from('diary_entries')
        .select(`
          user_id,
          user:users!user_id (
            id,
            name,
            email,
            role
          )
        `)
        .eq('is_published', true)
        .in('user.role', ['user', 'admin']) // In unified system, all users can create content
        .neq('user_id', user.id)
        .limit(5)

      if (writersError) {
        setError(`Error finding writers: ${writersError.message}`)
        setLoading(false)
        return
      }

      // Get unique writer IDs
      const uniqueWriterIds = [...new Set(writers?.map(w => w.user_id) || [])]
      
      if (uniqueWriterIds.length === 0) {
        setError('No writers with published content found')
        setLoading(false)
        return
      }

      // Create subscriptions
      let created = 0
      for (const writerId of uniqueWriterIds) {
        const { error: subError } = await supabase
          .from('subscriptions')
          .upsert({
            reader_id: user.id,
            writer_id: writerId,
            status: 'active',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          }, {
            onConflict: 'reader_id,writer_id'
          })

        if (!subError) {
          created++
        }
      }

      setMessage(`Successfully created ${created} subscriptions!`)
      
      // Redirect to timeline after 2 seconds
      setTimeout(() => {
        router.push('/timeline')
      }, 2000)

    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm">
        <h1 className="text-2xl font-serif text-gray-800 mb-4">Setup Test Subscriptions</h1>
        
        <p className="text-gray-600 mb-6">
          Click the button below to create test subscriptions to writers with content.
        </p>

        {message && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-lg">
            {message}
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        <button
          onClick={createTestSubscriptions}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Creating Subscriptions...' : 'Create Test Subscriptions'}
        </button>

        <button
          onClick={() => router.push('/timeline')}
          className="w-full mt-4 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300"
        >
          Go to Timeline
        </button>
      </div>
    </div>
  )
}