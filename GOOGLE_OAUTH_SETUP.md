# Google OAuth Setup for OnlyDiary

## 🔧 Supabase Configuration

You need to configure Google OAuth in your Supabase project dashboard:

### 1. Go to Supabase Dashboard
- Navigate to your project: https://supabase.com/dashboard/project/inzwekkutrwkcynvbyyu
- Go to Authentication → Providers

### 2. Enable Google Provider
- Find "Google" in the list of providers
- Toggle it to "Enabled"

### 3. Add Google OAuth Credentials
Use these exact values:

**Client ID:**
```
439495600988-8qfnslgcdvr6noa8icbo1rbv38fsilj1.apps.googleusercontent.com
```

**Client Secret:**
```
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
```

**Redirect URL (should be auto-filled):**
```
https://inzwekkutrwkcynvbyyu.supabase.co/auth/v1/callback
```

### 4. Save Configuration
Click "Save" to apply the changes.

## 🌍 Environment Variables

Add these to your `.env.local` file:

```env
# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=439495600988-8qfnslgcdvr6noa8icbo1rbv38fsilj1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

## ✅ What's Been Implemented

1. **Google OAuth Button Component** - Styled button with Google logo
2. **Register Page** - Google sign up option with "Continue with Google"
3. **Login Page** - Google sign in option with "Continue with Google"
4. **Auth Callback Handler** - Processes OAuth responses and creates user profiles
5. **User Profile Creation** - Automatically creates user records for Google OAuth users
6. **Welcome Email Integration** - Sends welcome emails to new Google users

## 🔄 User Flow

1. User clicks "Continue with Google" on login/register page
2. Redirected to Google OAuth consent screen
3. After approval, redirected to `/auth/callback`
4. Callback handler processes the authentication
5. User profile created if new user
6. Welcome email sent (if new user)
7. Redirected to dashboard

## 🧪 Testing

After configuring Supabase:

1. Start your development server: `npm run dev`
2. Go to http://localhost:3000/login or http://localhost:3000/register
3. Click "Continue with Google"
4. Complete Google OAuth flow
5. Should be redirected to dashboard

## 🚨 Important Notes

- The Google OAuth credentials are already configured for:
  - localhost:3000 (development)
  - onlydiary.app (production)
  - www.onlydiary.app (production with www)
  - Supabase callback URL

- Users who sign up with Google will have their profile automatically populated with:
  - Name from Google account
  - Avatar from Google account
  - Email from Google account
  - Default role: 'user'

## 🔍 Troubleshooting

If Google OAuth doesn't work:

1. **Check Supabase Configuration**: Ensure Google provider is enabled with correct credentials
2. **Check Environment Variables**: Ensure `.env.local` has the Google credentials
3. **Check Console Errors**: Look for authentication errors in browser console
4. **Check Redirect URLs**: Ensure all redirect URLs are properly configured in Google Console

The implementation is complete and ready to use once you configure the Supabase dashboard!
