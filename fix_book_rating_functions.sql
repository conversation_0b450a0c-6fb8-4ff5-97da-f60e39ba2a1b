-- Fix database functions to use pen_rating instead of rating column
-- This fixes the 400 Bad Request error when submitting reviews

-- Fix update_book_rating function
CREATE OR REPLACE FUNCTION update_book_rating()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update the project's average rating and review count
    UPDATE public.projects 
    SET 
        average_rating = (
            SELECT AVG(pen_rating)::DECIMAL(3,2) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Fix update_project_rating function (if it exists)
CREATE OR REPLACE FUNCTION update_project_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE projects 
    SET 
        average_rating = (
            SELECT COALESCE(AVG(pen_rating), 0) 
            FROM book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists and is properly set up
DROP TRIGGER IF EXISTS update_project_rating_trigger ON book_reviews;
CREATE TRIGGER update_project_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON book_reviews
    FOR EACH ROW EXECUTE FUNCTION update_book_rating();

-- Also ensure the other trigger name variant works
DROP TRIGGER IF EXISTS update_book_rating_trigger ON book_reviews;
CREATE TRIGGER update_book_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON book_reviews
    FOR EACH ROW EXECUTE FUNCTION update_book_rating();

SELECT 'Book rating functions fixed successfully - now using pen_rating column' as status;
