-- Fix audio URLs to use working R2 domain instead of CORS-blocked domain
-- Replace audio.onlydiary.app with pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev

-- Update audio_posts table
UPDATE audio_posts 
SET audio_url = REPLACE(audio_url, 'https://audio.onlydiary.app/', 'https://pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev/')
WHERE audio_url LIKE 'https://audio.onlydiary.app/%';

-- Update book_audio_posts table if it exists
UPDATE book_audio_posts 
SET audio_url = REPLACE(audio_url, 'https://audio.onlydiary.app/', 'https://pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev/')
WHERE audio_url LIKE 'https://audio.onlydiary.app/%';

-- Update audio_replies table if it exists
UPDATE audio_replies 
SET audio_url = REPLACE(audio_url, 'https://audio.onlydiary.app/', 'https://pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev/')
WHERE audio_url LIKE 'https://audio.onlydiary.app/%';

-- Update book_audio_replies table if it exists
UPDATE book_audio_replies 
SET audio_url = REPLACE(audio_url, 'https://audio.onlydiary.app/', 'https://pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev/')
WHERE audio_url LIKE 'https://audio.onlydiary.app/%';

-- Show what was updated
SELECT 'audio_posts' as table_name, COUNT(*) as updated_count 
FROM audio_posts 
WHERE audio_url LIKE 'https://pub-3aa1469fcbb64ad48c21750232ac8140.r2.dev/%';
