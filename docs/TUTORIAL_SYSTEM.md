# OnlyDiary Tutorial System

A brilliant, non-intrusive interactive tutorial system that helps users discover and master OnlyDiary's features without getting in the way.

## 🌟 Key Features

- **Non-Intrusive Design**: Floating trigger that blends seamlessly with your UI
- **Smart Contextual Guidance**: Shows relevant tutorials based on user's current page and progress
- **Spotlight Effects**: Highlights specific UI elements with smooth animations
- **Progressive Disclosure**: Reveals features as users are ready for them
- **Persistent Progress**: Remembers user progress and preferences across sessions
- **Mobile-First**: Fully responsive design that works on all devices
- **Accessibility**: Proper ARIA labels and keyboard navigation support

## 🏗️ Architecture

### Core Components

1. **TutorialContext** (`contexts/TutorialContext.tsx`)
   - Centralized state management for tutorial system
   - Handles step navigation, progress tracking, and persistence
   - Provides hooks for easy integration

2. **TutorialOverlay** (`components/TutorialOverlay.tsx`)
   - Main tutorial interface with spotlight effects
   - Responsive tooltip positioning
   - Smooth animations and transitions

3. **TutorialTrigger** (`components/TutorialTrigger.tsx`)
   - Floating help button with expandable menu
   - Category selection and progress overview
   - Auto-hide functionality for non-intrusive experience

4. **TutorialSettings** (`components/TutorialSettings.tsx`)
   - User preferences and tutorial management
   - Progress overview and category controls
   - Reset functionality

### Tutorial Categories

1. **Welcome Tour** 👋 - Platform introduction and core concepts
2. **Writing Guide** 📝 - Creating compelling diary entries
3. **Audio Features** 🎤 - Voice-based content creation
4. **Profile Setup** 👤 - Customization and monetization
5. **Discovery & Engagement** 🌟 - Finding and connecting with creators
6. **Advanced Features** 📚 - Books, analytics, and community building

## 🚀 Usage

### Basic Integration

The tutorial system is automatically integrated into the main layout:

```tsx
// app/layout.tsx
import { TutorialProvider } from '@/contexts/TutorialContext'
import { TutorialOverlay } from '@/components/TutorialOverlay'
import { TutorialTrigger } from '@/components/TutorialTrigger'

export default function RootLayout({ children }) {
  return (
    <TutorialProvider>
      {children}
      <TutorialOverlay />
      <TutorialTrigger />
    </TutorialProvider>
  )
}
```

### Using the Tutorial Hook

```tsx
import { useTutorial } from '@/contexts/TutorialContext'

function MyComponent() {
  const {
    startTutorial,
    startCategory,
    currentStepData,
    getProgress
  } = useTutorial()

  return (
    <div>
      <button onClick={() => startTutorial('welcome')}>
        Start Tutorial
      </button>
      <div data-tutorial="my-element">
        This element can be highlighted in tutorials
      </div>
    </div>
  )
}
```

### Smart Initialization

```tsx
import { useTutorialInit } from '@/hooks/useTutorialInit'

function Dashboard({ user }) {
  const { startContextualTutorial } = useTutorialInit({
    userId: user.id,
    isNewUser: user.isNew,
    hasCreatedContent: user.hasContent
  })

  // Tutorial will auto-start based on user context
}
```

## 🎯 Adding Tutorial Steps

### 1. Define Tutorial Targets

Add `data-tutorial` attributes to elements you want to highlight:

```tsx
<button data-tutorial="create-button">Create Entry</button>
<form data-tutorial="entry-form">...</form>
<section data-tutorial="media-upload">...</section>
```

### 2. Create Tutorial Steps

Add steps to `lib/tutorial-steps.ts`:

```tsx
{
  id: 'my-step-1',
  title: 'Welcome to Feature X',
  content: 'This is how you use this amazing feature...',
  target: '[data-tutorial="my-element"]',
  position: 'bottom',
  page: '/my-page',
  category: 'writing',
  order: 1
}
```

### 3. Step Properties

- `id`: Unique identifier for the step
- `title`: Step title shown in tooltip
- `content`: Detailed explanation text
- `target`: CSS selector for element to highlight (optional)
- `position`: Tooltip position relative to target ('top', 'bottom', 'left', 'right', 'center')
- `page`: Page where step should appear (optional)
- `category`: Tutorial category ('welcome', 'writing', 'audio', 'profile', 'discovery', 'advanced')
- `order`: Step order within category
- `action`: Expected user action ('click', 'hover', 'scroll', 'none')
- `optional`: Whether step can be skipped

## 🎨 Customization

### Styling

The tutorial system uses Tailwind CSS and follows your existing design system:

```tsx
// Custom spotlight colors
<div style={{
  boxShadow: `0 0 0 4px rgba(59, 130, 246, 0.3)` // Blue spotlight
}} />

// Custom tooltip themes
<div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl">
```

### Behavior

Customize tutorial behavior through the context:

```tsx
const { updatePreferences } = useTutorial()

updatePreferences({
  showTutorial: true,
  autoStart: false,
  showHints: true
})
```

## 📱 Mobile Optimization

- Touch-friendly 48px minimum target sizes
- Responsive tooltip positioning
- Swipe gestures for navigation
- Optimized for small screens

## ♿ Accessibility

- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Focus management

## 🔧 Development

### Testing Tutorial Steps

Use the demo component for testing:

```tsx
import { TutorialDemo } from '@/components/TutorialDemo'

// Add to any page during development
<TutorialDemo />
```

### Debugging

Enable debug mode in development:

```tsx
const tutorial = useTutorial()
console.log('Tutorial state:', tutorial.state)
console.log('Current step:', tutorial.currentStepData)
```

## 🚀 Best Practices

1. **Keep Steps Focused**: Each step should teach one specific concept
2. **Use Clear Language**: Write in friendly, conversational tone
3. **Progressive Disclosure**: Don't overwhelm users with too much at once
4. **Test on Mobile**: Ensure tutorials work well on small screens
5. **Respect User Choice**: Always provide easy ways to skip or exit
6. **Update Regularly**: Keep tutorial content current with feature changes

## 🔮 Future Enhancements

- **Analytics Integration**: Track tutorial completion rates and drop-off points
- **A/B Testing**: Test different tutorial flows and content
- **Personalization**: Adapt tutorials based on user behavior and preferences
- **Video Integration**: Add video explanations for complex features
- **Interactive Elements**: Allow users to practice actions within tutorials
- **Multi-language Support**: Internationalization for global users

## 📊 Monitoring

Track tutorial effectiveness:

- Completion rates by category
- User drop-off points
- Feature adoption after tutorial completion
- User feedback and satisfaction scores

The tutorial system is designed to grow with your platform and help users discover the full potential of OnlyDiary's unique features.
