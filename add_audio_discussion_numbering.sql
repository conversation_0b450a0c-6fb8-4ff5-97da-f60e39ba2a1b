-- Add sequential numbering to audio discussions
-- This adds a discussion_number field that auto-increments per chapter

-- 1. Add discussion_number column to book_audio_posts
ALTER TABLE book_audio_posts ADD COLUMN discussion_number INTEGER;

-- 2. <PERSON><PERSON> function to auto-assign discussion numbers per chapter
CREATE OR REPLACE FUNCTION assign_discussion_number()
R<PERSON><PERSON>NS TRIGGER AS $$
DECLARE
    next_number INTEGER;
BEGIN
    -- Get the next discussion number for this chapter
    SELECT COALESCE(MAX(discussion_number), 0) + 1 
    INTO next_number
    FROM book_audio_posts 
    WHERE chapter_id = NEW.chapter_id;
    
    -- Assign the number
    NEW.discussion_number := next_number;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. <PERSON><PERSON> trigger to auto-assign discussion numbers
DROP TRIGGER IF EXISTS assign_discussion_number_trigger ON book_audio_posts;
CREATE TRIGGER assign_discussion_number_trigger
    BEFORE INSERT ON book_audio_posts
    FOR EACH ROW EXECUTE FUNCTION assign_discussion_number();

-- 4. Backfill existing posts with discussion numbers
WITH numbered_posts AS (
    SELECT 
        id,
        ROW_NUMBER() OVER (PARTITION BY chapter_id ORDER BY created_at) as discussion_num
    FROM book_audio_posts
    WHERE discussion_number IS NULL
)
UPDATE book_audio_posts 
SET discussion_number = numbered_posts.discussion_num
FROM numbered_posts
WHERE book_audio_posts.id = numbered_posts.id;

-- 5. Add reply_number column to book_audio_replies for sequential reply numbering
ALTER TABLE book_audio_replies ADD COLUMN reply_number INTEGER;

-- 6. Create function to auto-assign reply numbers per post
CREATE OR REPLACE FUNCTION assign_reply_number()
RETURNS TRIGGER AS $$
DECLARE
    next_number INTEGER;
BEGIN
    -- Get the next reply number for this post
    SELECT COALESCE(MAX(reply_number), 0) + 1 
    INTO next_number
    FROM book_audio_replies 
    WHERE book_audio_post_id = NEW.book_audio_post_id;
    
    -- Assign the number
    NEW.reply_number := next_number;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger to auto-assign reply numbers
DROP TRIGGER IF EXISTS assign_reply_number_trigger ON book_audio_replies;
CREATE TRIGGER assign_reply_number_trigger
    BEFORE INSERT ON book_audio_replies
    FOR EACH ROW EXECUTE FUNCTION assign_reply_number();

-- 8. Backfill existing replies with reply numbers
WITH numbered_replies AS (
    SELECT 
        id,
        ROW_NUMBER() OVER (PARTITION BY book_audio_post_id ORDER BY created_at) as reply_num
    FROM book_audio_replies
    WHERE reply_number IS NULL
)
UPDATE book_audio_replies 
SET reply_number = numbered_replies.reply_num
FROM numbered_replies
WHERE book_audio_replies.id = numbered_replies.id;

-- 9. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_book_audio_posts_discussion_number ON book_audio_posts(chapter_id, discussion_number);
CREATE INDEX IF NOT EXISTS idx_book_audio_replies_reply_number ON book_audio_replies(book_audio_post_id, reply_number);

-- 10. Add comments for documentation
COMMENT ON COLUMN book_audio_posts.discussion_number IS 'Sequential discussion number per chapter, auto-assigned';
COMMENT ON COLUMN book_audio_replies.reply_number IS 'Sequential reply number per post, auto-assigned';
COMMENT ON FUNCTION assign_discussion_number() IS 'Auto-assigns sequential discussion numbers per chapter';
COMMENT ON FUNCTION assign_reply_number() IS 'Auto-assigns sequential reply numbers per post';
