-- Fix existing ChangeLog user
-- The user exists but might have incorrect data

-- First, let's see what the current user looks like
SELECT id, name, email, bio, avatar, price_monthly, role, created_at
FROM users 
WHERE email = '<EMAIL>';

-- Update the existing user to have the correct data
UPDATE users 
SET 
    name = 'OnlyDiary ChangeLog',
    bio = 'The Code Book - Official OnlyDiary development updates and feature announcements. Subscribe to stay updated on new features, improvements, and behind-the-scenes development insights.',
    avatar = '📝',
    price_monthly = NULL, -- Free changelog
    role = 'user',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Verify the update worked
SELECT id, name, email, bio, avatar, price_monthly, role
FROM users 
WHERE email = '<EMAIL>';

-- Check if there are any existing diary entries for this user
SELECT 
    u.id as user_id,
    u.name,
    COUNT(de.id) as entry_count,
    MAX(de.created_at) as latest_entry
FROM users u
LEFT JOIN diary_entries de ON u.id = de.user_id
WHERE u.email = '<EMAIL>'
GROUP BY u.id, u.name;

-- If no entries exist, create the welcome entry
DO $$
DECLARE
    changelog_user_id UUID;
    entry_count INTEGER;
BEGIN
    -- Get the ChangeLog user ID
    SELECT id INTO changelog_user_id 
    FROM users 
    WHERE email = '<EMAIL>';
    
    -- Check if any entries exist
    SELECT COUNT(*) INTO entry_count
    FROM diary_entries
    WHERE user_id = changelog_user_id;
    
    -- Only create welcome entry if none exist
    IF entry_count = 0 THEN
        INSERT INTO diary_entries (
            user_id,
            title,
            body_md,
            is_free,
            is_hidden,
            created_at,
            updated_at
        ) VALUES (
            changelog_user_id,
            'Welcome to The Code Book! 🎉',
            '# Welcome to The Code Book!

Hey everyone! 👋

This is The Code Book - OnlyDiary''s official development journal where I share daily updates about new features, improvements, and behind-the-scenes development insights.

## What to Expect

**Automated Updates**: Every time I push code changes, you''ll automatically see an update here. The system reads my git commit messages and translates them into user-friendly updates.

**Feature Announcements**: Be the first to know about new features as they''re deployed.

**Development Insights**: Get a peek behind the curtain at how OnlyDiary evolves daily.

**Community Feedback**: Your suggestions and feedback directly influence what gets built next.

## How It Works

- **Free Updates**: All changelog entries are free for everyone
- **Subscribe**: Hit the subscribe button to get these updates in your timeline
- **Timeline Integration**: Subscribed users will see changelog updates mixed in with their regular timeline
- **Automated**: No manual posting needed - updates happen with each deployment

## Privacy & Security

The system automatically filters out sensitive information like security fixes, internal system details, or anything that could expose the platform to risks.

## Let''s Build Together

OnlyDiary is built for you, and your feedback shapes every decision. Feel free to reach out with suggestions, bug reports, or just to say hi!

Ready to follow along on this journey? Hit that subscribe button! 🚀

---

*The Code Book is updated automatically with each deployment. Subscribe to never miss an update!*',
            true, -- Free entry
            false, -- Not hidden
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created welcome ChangeLog entry';
    ELSE
        RAISE NOTICE 'ChangeLog user already has % entries, skipping welcome entry creation', entry_count;
    END IF;
END $$;

-- Final verification
SELECT 
    u.id,
    u.name,
    u.email,
    u.bio,
    u.avatar,
    u.price_monthly,
    COUNT(de.id) as total_entries
FROM users u
LEFT JOIN diary_entries de ON u.id = de.user_id
WHERE u.email = '<EMAIL>'
GROUP BY u.id, u.name, u.email, u.bio, u.avatar, u.price_monthly;
