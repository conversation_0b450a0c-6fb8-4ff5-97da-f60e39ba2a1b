'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useTutorial } from '@/contexts/TutorialContext'

interface TutorialInitOptions {
  userId?: string
  isNewUser?: boolean
  hasCreatedContent?: boolean
  skipAutoStart?: boolean
}

/**
 * Smart tutorial initialization hook that determines when and how to show tutorials
 * based on user behavior, page context, and completion status.
 */
export function useTutorialInit({
  userId,
  isNewUser = false,
  hasCreatedContent = false,
  skipAutoStart = false
}: TutorialInitOptions = {}) {
  const pathname = usePathname()
  const {
    state,
    startTutorial,
    startCategory,
    isCategoryComplete,
    canShowOnPage,
    updatePreferences
  } = useTutorial()

  // Initialize tutorial preferences for new users
  useEffect(() => {
    if (isNewUser && userId && !state.hasSeenWelcome) {
      updatePreferences({
        showTutorial: true,
        autoStart: true,
        showHints: true
      })
    }
  }, [isNewUser, userId, state.hasSeenWelcome, updatePreferences])

  // Auto-start welcome tutorial for new users
  useEffect(() => {
    if (
      isNewUser &&
      !skipAutoStart &&
      state.userPreferences.autoStart &&
      state.userPreferences.showTutorial &&
      !state.hasSeenWelcome &&
      !isCategoryComplete('welcome') &&
      canShowOnPage(pathname)
    ) {
      // Small delay to let the page load
      const timer = setTimeout(() => {
        startTutorial('welcome')
      }, 1500)

      return () => clearTimeout(timer)
    }
  }, [
    isNewUser,
    skipAutoStart,
    state.userPreferences.autoStart,
    state.userPreferences.showTutorial,
    state.hasSeenWelcome,
    isCategoryComplete,
    canShowOnPage,
    pathname,
    startTutorial
  ])

  // Smart contextual tutorial suggestions based on page
  useEffect(() => {
    if (
      !state.userPreferences.showTutorial ||
      !state.userPreferences.autoStart ||
      state.isActive ||
      skipAutoStart
    ) {
      return
    }

    const suggestTutorialForPage = () => {
      // Dashboard page - suggest writing tutorial if user hasn't created content
      if (pathname === '/dashboard' && !hasCreatedContent && !isCategoryComplete('writing')) {
        const timer = setTimeout(() => {
          if (state.userPreferences.showHints) {
            startCategory('writing')
          }
        }, 3000)
        return () => clearTimeout(timer)
      }

      // Write page - suggest writing tutorial if not completed
      if (pathname.startsWith('/write') && !isCategoryComplete('writing')) {
        const timer = setTimeout(() => {
          startCategory('writing')
        }, 2000)
        return () => clearTimeout(timer)
      }

      // Timeline page - suggest discovery tutorial if not completed
      if (pathname === '/timeline' && !isCategoryComplete('discovery')) {
        const timer = setTimeout(() => {
          if (state.userPreferences.showHints) {
            startCategory('discovery')
          }
        }, 2500)
        return () => clearTimeout(timer)
      }

      // Profile edit page - suggest profile tutorial if not completed
      if (pathname === '/profile/edit' && !isCategoryComplete('profile')) {
        const timer = setTimeout(() => {
          startCategory('profile')
        }, 2000)
        return () => clearTimeout(timer)
      }

      // Books page - suggest advanced tutorial if user has created content
      if (pathname === '/books' && hasCreatedContent && !isCategoryComplete('advanced')) {
        const timer = setTimeout(() => {
          if (state.userPreferences.showHints) {
            startCategory('advanced')
          }
        }, 2500)
        return () => clearTimeout(timer)
      }
    }

    return suggestTutorialForPage()
  }, [
    pathname,
    hasCreatedContent,
    state.userPreferences.showTutorial,
    state.userPreferences.autoStart,
    state.userPreferences.showHints,
    state.isActive,
    skipAutoStart,
    isCategoryComplete,
    startCategory
  ])

  // Return utility functions for manual tutorial control
  return {
    // Check if tutorial should be shown for current context
    shouldShowTutorial: () => {
      return (
        state.userPreferences.showTutorial &&
        canShowOnPage(pathname) &&
        !state.isActive
      )
    },

    // Start contextual tutorial based on current page
    startContextualTutorial: () => {
      if (pathname === '/dashboard') {
        startCategory(hasCreatedContent ? 'advanced' : 'writing')
      } else if (pathname.startsWith('/write')) {
        startCategory('writing')
      } else if (pathname === '/timeline') {
        startCategory('discovery')
      } else if (pathname === '/profile/edit') {
        startCategory('profile')
      } else if (pathname === '/books') {
        startCategory('advanced')
      } else {
        startTutorial('welcome')
      }
    },

    // Get suggested next tutorial category
    getNextSuggestedCategory: () => {
      if (!isCategoryComplete('welcome')) return 'welcome'
      if (!hasCreatedContent && !isCategoryComplete('writing')) return 'writing'
      if (!isCategoryComplete('profile')) return 'profile'
      if (!isCategoryComplete('discovery')) return 'discovery'
      if (!isCategoryComplete('audio')) return 'audio'
      if (hasCreatedContent && !isCategoryComplete('advanced')) return 'advanced'
      return null
    },

    // Check if user is likely new and needs guidance
    isNewUserExperience: () => {
      return (
        isNewUser ||
        (!state.hasSeenWelcome && !isCategoryComplete('welcome'))
      )
    },

    // Get tutorial completion percentage
    getCompletionStatus: () => {
      const categories = ['welcome', 'writing', 'profile', 'discovery', 'audio', 'advanced']
      const completed = categories.filter(cat => isCategoryComplete(cat)).length
      return {
        completed,
        total: categories.length,
        percentage: Math.round((completed / categories.length) * 100)
      }
    }
  }
}

/**
 * Hook for detecting user's tutorial readiness based on their activity
 */
export function useTutorialReadiness(userId?: string) {
  const { state } = useTutorial()

  // This would typically connect to your user analytics/activity data
  // For now, we'll use localStorage to track basic activity
  useEffect(() => {
    if (!userId) return

    const activityKey = `user_activity_${userId}`
    const activity = localStorage.getItem(activityKey)
    
    if (!activity) {
      // New user - set initial activity tracking
      localStorage.setItem(activityKey, JSON.stringify({
        firstVisit: Date.now(),
        hasCreatedContent: false,
        hasVisitedDashboard: false,
        hasVisitedTimeline: false,
        tutorialStarted: false
      }))
    }
  }, [userId])

  return {
    // Check if user has completed basic onboarding actions
    hasCompletedOnboarding: () => {
      if (!userId) return false
      
      const activityKey = `user_activity_${userId}`
      const activity = localStorage.getItem(activityKey)
      
      if (!activity) return false
      
      try {
        const data = JSON.parse(activity)
        return data.hasCreatedContent && data.hasVisitedDashboard
      } catch {
        return false
      }
    },

    // Mark that user has completed a key action
    markActivity: (action: string) => {
      if (!userId) return
      
      const activityKey = `user_activity_${userId}`
      const activity = localStorage.getItem(activityKey)
      
      try {
        const data = activity ? JSON.parse(activity) : {}
        data[action] = true
        data.lastActivity = Date.now()
        localStorage.setItem(activityKey, JSON.stringify(data))
      } catch {
        // Ignore errors
      }
    }
  }
}
