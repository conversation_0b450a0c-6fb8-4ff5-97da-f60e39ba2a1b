'use client'

import { useEffect, useRef } from 'react'

interface UseEngagementTrackingProps {
  contentType: 'diary' | 'audio' | 'book'
  contentId: string
  enabled?: boolean
}

export function useEngagementTracking({ 
  contentType, 
  contentId, 
  enabled = true 
}: UseEngagementTrackingProps) {
  const hasTrackedImpression = useRef(false)

  // Track impression when component mounts and comes into view
  useEffect(() => {
    if (!enabled || hasTrackedImpression.current) return

    const trackImpression = async () => {
      try {
        await fetch('/api/engagement/impression', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contentType,
            contentId,
            source: 'component'
          })
        })
        hasTrackedImpression.current = true
      } catch (error) {
        console.error('Failed to track impression:', error)
      }
    }

    // Use Intersection Observer for better impression tracking
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasTrackedImpression.current) {
            trackImpression()
          }
        })
      },
      { threshold: 0.5 } // Track when 50% visible
    )

    // Find the element to observe (current component)
    const element = document.querySelector(`[data-content-id="${contentId}"]`)
    if (element) {
      observer.observe(element)
    }

    return () => {
      observer.disconnect()
    }
  }, [contentType, contentId, enabled])

  // Function to track clicks
  const trackClick = async (source = 'link') => {
    if (!enabled) return

    try {
      await fetch('/api/engagement/click', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
          source
        })
      })
    } catch (error) {
      console.error('Failed to track click:', error)
    }
  }

  return { trackClick }
}

// Hook for tracking shares (used by ShareButton)
export function useShareTracking() {
  const trackShare = async (
    contentType: 'diary' | 'audio' | 'book',
    contentId: string,
    platform: string
  ) => {
    try {
      await fetch('/api/engagement/share', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
          platform
        })
      })
    } catch (error) {
      console.error('Failed to track share:', error)
    }
  }

  return { trackShare }
}
