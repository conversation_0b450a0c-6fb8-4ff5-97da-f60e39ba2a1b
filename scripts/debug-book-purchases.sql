-- Debug script to check if book purchases are being recorded
-- Run this in Supabase SQL Editor

-- Check recent book purchases (especially free ones)
SELECT 
    bp.id,
    bp.user_id,
    bp.project_id,
    bp.purchase_price_cents,
    bp.status,
    bp.created_at,
    p.title,
    p.sales_count,
    u.name as user_name
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
LEFT JOIN users u ON bp.user_id = u.id
WHERE bp.purchase_price_cents = 0  -- Free books
ORDER BY bp.created_at DESC
LIMIT 10;

-- Check if the trigger is working by comparing book_purchases count vs sales_count
SELECT 
    p.id,
    p.title,
    p.sales_count,
    COUNT(bp.id) as actual_purchases,
    CASE 
        WHEN p.sales_count = COUNT(bp.id) THEN 'MATCH ✓'
        ELSE 'MISMATCH ✗'
    END as status
FROM projects p
LEFT JOIN book_purchases bp ON p.id = bp.project_id AND bp.status = 'completed'
WHERE p.is_ebook = true 
    AND p.is_complete = true
    AND (p.sales_count > 0 OR COUNT(bp.id) > 0)
GROUP BY p.id, p.title, p.sales_count
ORDER BY p.sales_count DESC;

-- Check if there are any book_purchases without status='completed'
SELECT 
    bp.status,
    COUNT(*) as count
FROM book_purchases bp
GROUP BY bp.status;
