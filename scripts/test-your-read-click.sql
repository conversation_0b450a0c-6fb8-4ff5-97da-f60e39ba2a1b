-- Test script to see if your recent "Read" click worked
-- This will show all users and their book purchases

-- 1. Find all users (look for yourself)
SELECT id, name, email FROM users ORDER BY created_at DESC LIMIT 10;

-- 2. Check recent book purchases by all users
SELECT
    bp.id,
    bp.project_id,
    bp.purchase_price_cents,
    bp.purchased_at,
    p.title,
    u.name as user_name,
    u.email
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
JOIN users u ON bp.user_id = u.id
ORDER BY bp.purchased_at DESC
LIMIT 10;

-- 3. Check the "RIP David Weaver" book specifically
SELECT
    id,
    title,
    sales_count,
    price_amount,
    is_ebook,
    is_complete
FROM projects
WHERE title ILIKE '%david weaver%' OR title ILIKE '%rip%';

-- 4. Check who has "purchased" the David Weaver book
SELECT
    bp.id,
    bp.purchased_at,
    bp.purchase_price_cents,
    u.name as user_name,
    u.email,
    'Already purchased' as status
FROM book_purchases bp
JOIN projects p ON bp.project_id = p.id
JOIN users u ON bp.user_id = u.id
WHERE p.title ILIKE '%david weaver%' OR p.title ILIKE '%rip%'
ORDER BY bp.purchased_at DESC;
