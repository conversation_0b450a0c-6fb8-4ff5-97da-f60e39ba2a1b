-- Test script to manually trigger a book purchase and see if sales_count updates
-- Replace 'YOUR_USER_ID' and 'BOOK_ID' with actual values

-- First, check current sales count for a specific book
SELECT 
    id,
    title,
    sales_count
FROM projects 
WHERE title ILIKE '%david weaver%' OR title ILIKE '%rip%'
LIMIT 1;

-- Insert a test purchase (replace the IDs below with real ones)
-- Get your user ID first:
SELECT id, name FROM users WHERE name ILIKE '%your_name%' LIMIT 5;

-- Get the book ID:
SELECT id, title FROM projects WHERE title ILIKE '%david weaver%' OR title ILIKE '%rip%' LIMIT 1;

-- Then insert a test purchase (UNCOMMENT AND REPLACE IDs):
/*
INSERT INTO book_purchases (
    user_id, 
    project_id, 
    purchase_price_cents, 
    status
) VALUES (
    'YOUR_USER_ID_HERE',  -- Replace with your actual user ID
    'BOOK_ID_HERE',       -- Replace with the book ID
    0,                    -- Free book
    'completed'           -- Important: must be 'completed'
) ON CONFLICT (user_id, project_id) DO NOTHING;
*/

-- Check if sales_count updated after the insert
SELECT 
    id,
    title,
    sales_count
FROM projects 
WHERE title ILIKE '%david weaver%' OR title ILIKE '%rip%'
LIMIT 1;
