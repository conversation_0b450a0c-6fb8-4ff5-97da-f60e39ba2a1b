#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate a secure token for cron endpoint authentication
 * Usage: node scripts/generate-cron-token.js
 */

const crypto = require('crypto')

function generateSecureToken() {
  try {
    console.log('🔐 Generating secure cron authentication token...')
    
    // Generate a cryptographically secure random token
    const token = crypto.randomBytes(32).toString('hex')
    
    console.log('\n✅ Secure token generated successfully!')
    console.log('\n📋 Add this to your environment variables:')
    console.log(`CRON_SECRET_TOKEN=${token}`)
    
    console.log('\n🔧 For Vercel Cron Jobs, use this format:')
    console.log(`Authorization: Bearer ${token}`)
    
    console.log('\n📝 Example Vercel cron configuration (vercel.json):')
    console.log(`{
  "crons": [
    {
      "path": "/api/cron/process-notifications",
      "schedule": "0 */6 * * *",
      "headers": {
        "Authorization": "Bearer ${token}"
      }
    }
  ]
}`)
    
    console.log('\n⚠️  Important Security Notes:')
    console.log('1. Keep this token secure and never commit it to version control')
    console.log('2. Use different tokens for development and production')
    console.log('3. Rotate tokens regularly (monthly recommended)')
    console.log('4. If compromised, generate a new token immediately')
    
    console.log('\n🧪 Optional: Generate test endpoint token as well?')
    console.log('Run: node scripts/generate-test-token.js')
    
  } catch (error) {
    console.error('❌ Error generating token:', error.message)
    process.exit(1)
  }
}

generateSecureToken()
