-- Manual script to update bestsellers immediately
-- Run this in Supabase SQL Editor to populate the bestsellers

-- Call the function to update daily bestsellers
SELECT update_daily_bestsellers();

-- Verify the results
SELECT 
    date,
    book_type,
    rank,
    title,
    author_name,
    sales_count
FROM daily_bestsellers 
WHERE date >= CURRENT_DATE - INTERVAL '1 day'
ORDER BY book_type, rank;

-- Check if we have any books with sales > 0
SELECT 
    title,
    sales_count,
    price_amount,
    CASE 
        WHEN price_amount IS NULL OR price_amount = 0 THEN 'free'
        ELSE 'paid'
    END as book_type
FROM projects 
WHERE is_ebook = true 
    AND is_complete = true 
    AND sales_count > 0
ORDER BY sales_count DESC;
