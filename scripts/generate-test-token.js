#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate a secure token for test endpoint authentication
 * Usage: node scripts/generate-test-token.js
 */

const crypto = require('crypto')

function generateTestToken() {
  try {
    console.log('🧪 Generating secure test endpoint authentication token...')
    
    // Generate a cryptographically secure random token
    const token = crypto.randomBytes(16).toString('hex') // Shorter for test endpoints
    
    console.log('\n✅ Test token generated successfully!')
    console.log('\n📋 Add this to your development .env.local:')
    console.log(`TEST_ENDPOINT_TOKEN=${token}`)
    
    console.log('\n🔧 Usage examples:')
    console.log(`curl -H "Authorization: Bearer ${token}" http://localhost:3000/api/test-aws`)
    console.log(`curl -H "Authorization: Bearer ${token}" http://localhost:3000/api/create-test-subscriptions`)
    
    console.log('\n⚠️  Important Notes:')
    console.log('1. This token is for DEVELOPMENT ONLY')
    console.log('2. Test endpoints are automatically disabled in production')
    console.log('3. Optional - test endpoints work without token if not set')
    console.log('4. Use this for additional security in shared development environments')
    
  } catch (error) {
    console.error('❌ Error generating test token:', error.message)
    process.exit(1)
  }
}

generateTestToken()
