#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate a bcrypt hash for the admin password
 * Usage: node scripts/generate-admin-hash.js "YourSecurePassword123!"
 */

const bcrypt = require('bcryptjs')

async function generateHash() {
  const password = process.argv[2]
  
  if (!password) {
    console.error('❌ Error: Please provide a password as an argument')
    console.log('Usage: node scripts/generate-admin-hash.js "YourSecurePassword123!"')
    process.exit(1)
  }

  if (password.length < 12) {
    console.error('❌ Error: Password must be at least 12 characters long')
    process.exit(1)
  }

  try {
    console.log('🔐 Generating secure hash for admin password...')
    
    // Use cost factor of 12 (good balance of security and performance)
    const hash = await bcrypt.hash(password, 12)
    
    console.log('\n✅ Hash generated successfully!')
    console.log('\n📋 Add this to your .env.local file:')
    console.log(`ADMIN_PASSWORD_HASH=${hash}`)
    console.log('\n⚠️  Important:')
    console.log('1. Keep this hash secure and never commit it to version control')
    console.log('2. The original password will not be stored anywhere')
    console.log('3. If you lose the password, you\'ll need to generate a new hash')
    
  } catch (error) {
    console.error('❌ Error generating hash:', error.message)
    process.exit(1)
  }
}

generateHash()
