#!/usr/bin/env node

/**
 * Automated ChangeLog Update Script
 * 
 * This script automatically creates changelog entries from git commits.
 * It can be run manually or integrated into your CI/CD pipeline.
 * 
 * Usage:
 *   node scripts/update-changelog.js
 *   node scripts/update-changelog.js --since="2024-01-01"
 *   node scripts/update-changelog.js --commits="abc123,def456"
 */

const { execSync } = require('child_process')
const https = require('https')
const http = require('http')

// Configuration
const CONFIG = {
  // Your OnlyDiary domain
  domain: process.env.ONLYDIARY_DOMAIN || 'https://www.onlydiary.app',
  
  // API endpoint for changelog updates
  endpoint: '/api/changelog/auto-update',
  
  // Git settings
  defaultSince: '1 day ago', // How far back to look for commits
  
  // Deployment info
  deploymentId: process.env.DEPLOYMENT_ID || `deploy-${Date.now()}`,
  environment: process.env.NODE_ENV || 'production'
}

/**
 * Get git commits since a specific time or commit
 */
function getGitCommits(since = CONFIG.defaultSince) {
  try {
    console.log(`📝 Getting git commits since: ${since}`)

    // Use a delimiter to separate commits and avoid JSON parsing issues with multi-line bodies
    const delimiter = '---COMMIT-SEPARATOR---'
    const gitCommand = `git log --since="${since}" --pretty=format:'${delimiter}%n{"hash":"%H","short":"%h","author":"%an","email":"%ae","date":"%ai","message":"%s"}' --no-merges`

    const output = execSync(gitCommand, { encoding: 'utf8' })

    if (!output.trim()) {
      console.log('ℹ️  No new commits found')
      return []
    }

    // Split by delimiter and parse each commit
    const commits = output
      .split(delimiter)
      .filter(section => section.trim()) // Remove empty sections
      .map(section => {
        const jsonLine = section.trim()
        if (!jsonLine) return null

        try {
          return JSON.parse(jsonLine)
        } catch (e) {
          console.warn('⚠️  Failed to parse commit JSON:', jsonLine.substring(0, 100) + '...')
          return null
        }
      })
      .filter(Boolean)

    console.log(`✅ Found ${commits.length} commits`)
    return commits

  } catch (error) {
    console.error('❌ Error getting git commits:', error.message)
    return []
  }
}

/**
 * Get specific commits by hash
 */
function getSpecificCommits(commitHashes) {
  try {
    console.log(`📝 Getting specific commits: ${commitHashes.join(', ')}`)

    const commits = []

    for (const hash of commitHashes) {
      try {
        const gitCommand = `git show --pretty=format:'{"hash":"%H","short":"%h","author":"%an","email":"%ae","date":"%ai","message":"%s"}' --no-patch ${hash}`
        const output = execSync(gitCommand, { encoding: 'utf8' })

        const commit = JSON.parse(output.trim())
        commits.push(commit)
      } catch (e) {
        console.warn(`⚠️  Failed to get commit ${hash}:`, e.message)
      }
    }

    console.log(`✅ Retrieved ${commits.length} commits`)
    return commits

  } catch (error) {
    console.error('❌ Error getting specific commits:', error.message)
    return []
  }
}

/**
 * Send commits to the changelog API
 */
async function updateChangelog(commits) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      commits,
      deployment_id: CONFIG.deploymentId,
      environment: CONFIG.environment
    })
    
    const url = new URL(CONFIG.endpoint, CONFIG.domain)
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data)
      }
    }
    
    console.log(`🚀 Sending ${commits.length} commits to ${CONFIG.domain}${CONFIG.endpoint}`)

    // Use http or https based on the protocol
    const requestModule = url.protocol === 'https:' ? https : http
    const req = requestModule.request(options, (res) => {
      let responseData = ''
      
      res.on('data', (chunk) => {
        responseData += chunk
      })
      
      res.on('end', () => {
        try {
          const response = JSON.parse(responseData)
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${response.error || responseData}`))
          }
        } catch (e) {
          reject(new Error(`Failed to parse response: ${responseData}`))
        }
      })
    })
    
    req.on('error', (error) => {
      reject(error)
    })
    
    req.write(data)
    req.end()
  })
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🎯 OnlyDiary Automated ChangeLog Update')
    console.log('=====================================')
    
    // Parse command line arguments
    const args = process.argv.slice(2)
    const sinceArg = args.find(arg => arg.startsWith('--since='))
    const commitsArg = args.find(arg => arg.startsWith('--commits='))
    
    let commits = []
    
    if (commitsArg) {
      // Get specific commits
      const commitHashes = commitsArg.replace('--commits=', '').split(',')
      commits = getSpecificCommits(commitHashes)
    } else {
      // Get commits since a time period
      const since = sinceArg ? sinceArg.replace('--since=', '') : CONFIG.defaultSince
      commits = getGitCommits(since)
    }
    
    if (commits.length === 0) {
      console.log('✅ No commits to process. ChangeLog is up to date!')
      return
    }
    
    // Show commits that will be processed
    console.log('\n📋 Commits to process:')
    commits.forEach((commit, index) => {
      console.log(`  ${index + 1}. ${commit.short} - ${commit.message}`)
    })
    
    // Send to API
    console.log('\n🔄 Updating changelog...')
    const result = await updateChangelog(commits)
    
    console.log('\n✅ ChangeLog update completed!')
    console.log(`   Processed: ${result.processed}/${result.total_commits} commits`)
    console.log(`   Filtered: ${result.filtered_commits} commits after filtering`)
    
    if (result.results) {
      const successful = result.results.filter(r => r.success).length
      const failed = result.results.filter(r => !r.success).length
      const skipped = result.results.filter(r => r.skipped).length
      
      console.log(`   Successful: ${successful}`)
      if (skipped > 0) console.log(`   Skipped: ${skipped} (already processed)`)
      if (failed > 0) console.log(`   Failed: ${failed}`)
    }
    
    console.log(`\n🌐 View changelog: ${CONFIG.domain}/changelog`)
    
  } catch (error) {
    console.error('\n❌ ChangeLog update failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = { getGitCommits, getSpecificCommits, updateChangelog }
