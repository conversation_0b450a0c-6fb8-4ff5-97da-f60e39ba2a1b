const webpush = require('web-push');

try {
  const vapidKeys = webpush.generateVAPIDKeys();
  
  console.log('=== VAPID Keys Generated ===');
  console.log('');
  console.log('Add these to your .env.local file:');
  console.log('');
  console.log(`NEXT_PUBLIC_VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  console.log(`VAPID_PRIVATE_KEY=${vapidKeys.privateKey}`);
  console.log('');
  console.log('=== Copy the lines above to your .env.local ===');
  
} catch (error) {
  console.error('Error generating VAPID keys:', error);
  console.log('Make sure web-push is installed: npm install web-push');
}
