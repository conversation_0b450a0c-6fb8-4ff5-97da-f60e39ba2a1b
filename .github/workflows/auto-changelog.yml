name: Auto-Update ChangeLog

# Trigger on successful deployments to main branch
on:
  push:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      since:
        description: 'Get commits since (e.g., "2024-01-01", "1 week ago")'
        required: false
        default: '1 day ago'
      force:
        description: 'Force update even if no new commits'
        required: false
        default: false
        type: boolean

jobs:
  update-changelog:
    runs-on: ubuntu-latest
    
    # Only run after successful deployment
    # You might want to add a condition here based on your deployment workflow
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        # Fetch full history for git log
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Configure git
      run: |
        git config --global user.name "OnlyDiary Bot"
        git config --global user.email "<EMAIL>"
    
    - name: Get deployment info
      id: deployment
      run: |
        echo "deployment_id=deploy-$(date +%s)-${{ github.sha }}" >> $GITHUB_OUTPUT
        echo "environment=${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}" >> $GITHUB_OUTPUT
        echo "domain=${{ github.ref == 'refs/heads/main' && 'https://www.onlydiary.app' || 'https://staging.onlydiary.app' }}" >> $GITHUB_OUTPUT
    
    - name: Update ChangeLog (Auto)
      if: github.event_name == 'push'
      env:
        ONLYDIARY_DOMAIN: ${{ steps.deployment.outputs.domain }}
        DEPLOYMENT_ID: ${{ steps.deployment.outputs.deployment_id }}
        NODE_ENV: ${{ steps.deployment.outputs.environment }}
      run: |
        echo "🎯 Auto-updating changelog for push to ${{ github.ref_name }}"
        node scripts/update-changelog.js
    
    - name: Update ChangeLog (Manual)
      if: github.event_name == 'workflow_dispatch'
      env:
        ONLYDIARY_DOMAIN: ${{ steps.deployment.outputs.domain }}
        DEPLOYMENT_ID: ${{ steps.deployment.outputs.deployment_id }}
        NODE_ENV: ${{ steps.deployment.outputs.environment }}
      run: |
        echo "🎯 Manual changelog update since: ${{ github.event.inputs.since }}"
        node scripts/update-changelog.js --since="${{ github.event.inputs.since }}"
    
    - name: Check ChangeLog Status
      env:
        ONLYDIARY_DOMAIN: ${{ steps.deployment.outputs.domain }}
      run: |
        echo "📊 Checking changelog status..."
        curl -s "$ONLYDIARY_DOMAIN/api/changelog/auto-update" | jq '.'
    
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ ChangeLog update failed!"
        echo "This is not critical - the deployment was successful."
        echo "You can manually run: node scripts/update-changelog.js"
    
    - name: Summary
      if: success()
      run: |
        echo "✅ ChangeLog updated successfully!"
        echo "🌐 View at: ${{ steps.deployment.outputs.domain }}/changelog"
        echo "📊 Admin: ${{ steps.deployment.outputs.domain }}/admin (ChangeLog tab)"
