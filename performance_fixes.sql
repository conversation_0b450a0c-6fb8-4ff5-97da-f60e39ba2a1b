-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> FIXES FOR ONLYDIARY DATABASE
-- Run these queries to resolve performance issues

-- 1. REALTIME PERFORMANCE FIXES
-- Check current realtime subscriptions
SELECT 
    schemaname,
    tablename,
    publication_name
FROM pg_publication_tables 
WHERE publication_name = 'supabase_realtime';

-- Disable realtime for tables that don't need it
-- (Only enable for tables that actually need real-time updates)
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS users;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS diary_entries;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS projects;

-- Re-enable only for essential real-time tables
-- ALTER PUBLICATION supabase_realtime ADD TABLE comments;
-- ALTER PUBLICATION supabase_realtime ADD TABLE reactions;

-- 2. BADGE SYSTEM PERFORMANCE FIXES
-- Add composite indexes for badge queries
CREATE INDEX IF NOT EXISTS idx_users_badge_tier_signup ON users(badge_tier, signup_number) 
WHER<PERSON> has_day1_badge = TRUE;

CREATE INDEX IF NOT EXISTS idx_users_day1_badge_created ON users(has_day1_badge, created_at) 
WHERE has_day1_badge = TRUE;

-- 3. OPTIMIZE BADGE STATISTICS FUNCTION
-- Replace the existing function with a more efficient version
CREATE OR REPLACE FUNCTION get_badge_statistics()
RETURNS TABLE (
    badge_tier TEXT,
    tier_name TEXT,
    count BIGINT,
    signup_range TEXT,
    max_possible INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH badge_counts AS (
        SELECT
            u.badge_tier,
            COUNT(*) as count
        FROM users u
        WHERE u.has_day1_badge = TRUE
        GROUP BY u.badge_tier
    )
    SELECT
        bc.badge_tier,
        CASE bc.badge_tier
            WHEN 'day1' THEN 'Day 1'
            WHEN 'pioneer' THEN 'Pioneer'
            WHEN 'founder' THEN 'Founder'
            WHEN 'early_adopter' THEN 'Early Adopter'
            WHEN 'charter_member' THEN 'Charter Member'
            ELSE 'Unknown'
        END as tier_name,
        bc.count,
        CASE bc.badge_tier
            WHEN 'day1' THEN '1-500'
            WHEN 'pioneer' THEN '501-1,000'
            WHEN 'founder' THEN '1,001-2,500'
            WHEN 'early_adopter' THEN '2,501-5,000'
            WHEN 'charter_member' THEN '5,001-10,000'
            ELSE 'N/A'
        END as signup_range,
        CASE bc.badge_tier
            WHEN 'day1' THEN 500
            WHEN 'pioneer' THEN 500
            WHEN 'founder' THEN 1500
            WHEN 'early_adopter' THEN 2500
            WHEN 'charter_member' THEN 5000
            ELSE 0
        END as max_possible
    FROM badge_counts bc
    ORDER BY 
        CASE bc.badge_tier
            WHEN 'day1' THEN 1
            WHEN 'pioneer' THEN 2
            WHEN 'founder' THEN 3
            WHEN 'early_adopter' THEN 4
            WHEN 'charter_member' THEN 5
            ELSE 6
        END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. OPTIMIZE BADGE ADMIN QUERIES
-- Create a materialized view for badge statistics (refresh periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS badge_stats_cache AS
SELECT 
    badge_tier,
    COUNT(*) as holders,
    MIN(signup_number) as min_signup,
    MAX(signup_number) as max_signup,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE has_day1_badge = TRUE) as percentage
FROM users 
WHERE has_day1_badge = TRUE 
GROUP BY badge_tier;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_badge_stats_cache_tier ON badge_stats_cache(badge_tier);

-- Function to refresh badge stats cache
CREATE OR REPLACE FUNCTION refresh_badge_stats_cache()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY badge_stats_cache;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. GENERAL PERFORMANCE IMPROVEMENTS
-- Add missing indexes for common queries
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_email_lower ON users(lower(email));

-- 6. MONITOR SLOW QUERIES
-- Enable pg_stat_statements if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Query to find slow queries (run this periodically)
-- SELECT 
--     query,
--     calls,
--     total_time,
--     mean_time,
--     rows
-- FROM pg_stat_statements 
-- WHERE mean_time > 100  -- queries taking more than 100ms on average
-- ORDER BY total_time DESC 
-- LIMIT 10;

-- 7. VACUUM AND ANALYZE
-- Run these periodically to maintain performance
-- VACUUM ANALYZE users;
-- VACUUM ANALYZE diary_entries;
-- VACUUM ANALYZE projects;

-- 8. CONNECTION POOLING RECOMMENDATIONS
-- Consider implementing connection pooling if not already done
-- Recommended settings for Supabase:
-- max_connections = 100
-- shared_preload_libraries = 'pg_stat_statements'
-- track_activity_query_size = 2048
