# 🛡️ CSRF Protection & Rate Limiting Implementation

## Security Vulnerabilities Fixed: #3 & #8

### Previous Issues ❌
- **CSRF:** No protection against cross-site request forgery attacks
- **Rate Limiting:** No protection against DoS, brute force, or API abuse
- **Unlimited Requests:** Endpoints could be overwhelmed with requests
- **No Authentication Throttling:** Brute force attacks possible

### Security Improvements Implemented ✅

## 1. **CSRF (Cross-Site Request Forgery) Protection**

### **CSRF Token Generation**
```typescript
// GET /api/csrf-token
// Returns: { csrfToken: "abc123...", message: "..." }
// Sets HttpOnly cookie with secret
```

### **CSRF Validation**
```typescript
import { validateCSRFProtection } from '@/lib/utils/csrf'

const csrfResult = validateCSRFProtection(request)
if (!csrfResult.isValid) {
  return new Response(JSON.stringify({ error: 'CSRF validation failed' }), { status: 403 })
}
```

### **CSRF Protection Features:**
- ✅ **Cryptographically secure tokens** (32-byte random)
- ✅ **HttpOnly cookies** for secret storage
- ✅ **Same-origin request detection** (automatic bypass)
- ✅ **Timing-safe comparison** (prevents timing attacks)
- ✅ **Development mode support** (localhost bypass)

### **Client-Side Usage:**
```javascript
// 1. Get CSRF token
const response = await fetch('/api/csrf-token')
const { csrfToken } = await response.json()

// 2. Include in requests
fetch('/api/protected-endpoint', {
  method: 'POST',
  headers: {
    'X-CSRF-Token': csrfToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
})
```

## 2. **Rate Limiting Protection**

### **Rate Limit Configurations:**
```typescript
AUTH: {
  windowMs: 15 * 60 * 1000,  // 15 minutes
  maxRequests: 5,            // 5 attempts per window
  message: 'Too many authentication attempts. Please try again in 15 minutes.'
}

API: {
  windowMs: 1 * 60 * 1000,   // 1 minute  
  maxRequests: 60,           // 60 requests per minute
  message: 'Too many API requests. Please slow down.'
}

UPLOAD: {
  windowMs: 5 * 60 * 1000,   // 5 minutes
  maxRequests: 10,           // 10 uploads per 5 minutes
  message: 'Too many upload attempts. Please wait before uploading again.'
}

PUBLIC: {
  windowMs: 1 * 60 * 1000,   // 1 minute
  maxRequests: 100,          // 100 requests per minute
  message: 'Too many requests. Please slow down.'
}

ADMIN: {
  windowMs: 5 * 60 * 1000,   // 5 minutes
  maxRequests: 20,           // 20 requests per 5 minutes
  message: 'Too many admin requests. Access temporarily restricted.'
}
```

### **Rate Limiting Features:**
- ✅ **IP-based tracking** (supports proxies/CDNs)
- ✅ **Sliding window** rate limiting
- ✅ **Automatic cleanup** of expired entries
- ✅ **Configurable limits** per endpoint type
- ✅ **Standard headers** (X-RateLimit-*)

### **Rate Limit Headers:**
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1640995200
Retry-After: 60
```

## 3. **Endpoints Protected**

### **CSRF Protection Applied To:**
- All POST/PUT/PATCH/DELETE endpoints (automatic)
- Cross-origin requests (same-origin bypassed)
- Form submissions and API calls

### **Rate Limiting Applied To:**
✅ **Admin Authentication** (`/api/admin/auth`) - AUTH limits (5/15min)  
✅ **Video Uploads** (`/api/r2/upload-video`) - UPLOAD limits (10/5min)  
✅ **Audio Uploads** (`/api/audio/upload`) - UPLOAD limits (10/5min)  
🔄 **API Endpoints** - Ready to apply API limits (60/min)  
🔄 **Public Endpoints** - Ready to apply PUBLIC limits (100/min)  

### **Implementation Pattern:**
```typescript
import { checkRateLimit, createRateLimitResponse, createRateLimitHeaders } from '@/lib/utils/rate-limit'

export async function POST(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = checkRateLimit(request, 'API', 'endpoint-name')
  
  if (!rateLimitResult.allowed) {
    return createRateLimitResponse('API', rateLimitResult)
  }
  
  // Process request...
  
  // Return response with rate limit headers
  const rateLimitHeaders = createRateLimitHeaders(rateLimitResult)
  return NextResponse.json(data, { headers: rateLimitHeaders })
}
```

## 4. **Attack Vectors Mitigated**

### 🚫 **Cross-Site Request Forgery (CSRF)**
- **Before:** Malicious sites could make requests on behalf of users
- **After:** CSRF tokens required for state-changing operations

### 🚫 **Denial of Service (DoS)**
- **Before:** Unlimited requests could overwhelm server
- **After:** Rate limits prevent resource exhaustion

### 🚫 **Brute Force Attacks**
- **Before:** Unlimited authentication attempts
- **After:** 5 attempts per 15 minutes for admin login

### 🚫 **API Abuse**
- **Before:** No limits on API usage
- **After:** Configurable limits per endpoint type

### 🚫 **Upload Flooding**
- **Before:** Unlimited file uploads
- **After:** 10 uploads per 5 minutes

## 5. **Client Integration**

### **CSRF Token Management:**
```javascript
// Store CSRF token globally
let csrfToken = null

// Get token on app initialization
async function initializeCSRF() {
  const response = await fetch('/api/csrf-token')
  const data = await response.json()
  csrfToken = data.csrfToken
}

// Use in all state-changing requests
function makeProtectedRequest(url, data) {
  return fetch(url, {
    method: 'POST',
    headers: {
      'X-CSRF-Token': csrfToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
}
```

### **Rate Limit Handling:**
```javascript
async function makeRequest(url, options) {
  const response = await fetch(url, options)
  
  // Check rate limit headers
  const remaining = response.headers.get('X-RateLimit-Remaining')
  const reset = response.headers.get('X-RateLimit-Reset')
  
  if (response.status === 429) {
    const retryAfter = response.headers.get('Retry-After')
    throw new Error(`Rate limited. Retry after ${retryAfter} seconds.`)
  }
  
  // Warn when approaching limit
  if (remaining && parseInt(remaining) < 10) {
    console.warn(`Approaching rate limit. ${remaining} requests remaining.`)
  }
  
  return response
}
```

## 6. **Production Considerations**

### **CSRF in Production:**
- ✅ **HTTPS Required** - Secure cookies need HTTPS
- ✅ **Domain Validation** - Same-origin checks work correctly
- ✅ **Token Rotation** - Tokens expire and regenerate

### **Rate Limiting in Production:**
- ⚠️ **Memory Store** - Current implementation uses in-memory storage
- 🔄 **Redis Recommended** - For multiple server instances
- ✅ **CDN Integration** - Works with Cloudflare/proxy headers

### **Scaling Considerations:**
```typescript
// For production with Redis:
import Redis from 'ioredis'
const redis = new Redis(process.env.REDIS_URL)

// Store rate limit data in Redis instead of memory
await redis.setex(key, windowMs / 1000, JSON.stringify(rateLimitData))
```

## 7. **Monitoring & Alerts**

### **Rate Limit Monitoring:**
- Track rate limit violations by IP
- Monitor for unusual traffic patterns
- Alert on sustained high request rates

### **CSRF Monitoring:**
- Log CSRF validation failures
- Track cross-origin request patterns
- Alert on CSRF attack attempts

### **Metrics to Track:**
- Rate limit hit rates by endpoint
- CSRF token generation/validation rates
- Failed authentication attempts
- Upload request patterns

---

**Status:** ✅ **IMPLEMENTED**  
**CSRF Protection:** Active on all state-changing endpoints  
**Rate Limiting:** Applied to critical endpoints (auth, uploads)  
**Risk Level:** 🟢 **LOW** (Previously MEDIUM-HIGH)  
**Next Steps:** Apply rate limiting to remaining API endpoints as needed
