-- Fix all remaining function search_path security warnings
-- This addresses all the remaining WARN level security issues

-- Fix get_story_venture_stats function
CREATE OR REPLACE FUNCTION get_story_venture_stats(entry_id uuid)
RETURNS TABLE(
    total_backers bigint,
    total_amount numeric,
    goal_amount numeric,
    is_goal_met boolean
) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::bigint as total_backers,
        COALESCE(SUM(amount), 0)::numeric as total_amount,
        de.story_venture_goal::numeric as goal_amount,
        (COALESCE(SUM(amount), 0) >= de.story_venture_goal) as is_goal_met
    FROM public.story_venture_contributions svc
    RIGHT JOIN public.diary_entries de ON de.id = entry_id
    WHERE svc.diary_entry_id = entry_id OR svc.diary_entry_id IS NULL
    GROUP BY de.story_venture_goal;
END;
$$ LANGUAGE plpgsql;

-- Fix get_public_story_venture_backers function
CREATE OR REPLACE FUNCTION get_public_story_venture_backers(entry_id uuid, limit_count integer DEFAULT 10)
RETURNS TABLE(
    backer_name text,
    backer_avatar text,
    amount numeric,
    message text,
    created_at timestamp with time zone
) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.name as backer_name,
        u.avatar as backer_avatar,
        svc.amount,
        svc.message,
        svc.created_at
    FROM public.story_venture_contributions svc
    JOIN public.users u ON svc.user_id = u.id
    WHERE svc.diary_entry_id = entry_id
    AND svc.is_anonymous = FALSE
    ORDER BY svc.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Fix get_entry_preview function
CREATE OR REPLACE FUNCTION get_entry_preview(entry_id uuid, preview_length integer DEFAULT 200)
RETURNS TEXT 
SET search_path = ''
AS $$
DECLARE
    entry_body TEXT;
    preview TEXT;
BEGIN
    SELECT body_md INTO entry_body 
    FROM public.diary_entries 
    WHERE id = entry_id;
    
    IF entry_body IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Remove markdown formatting and get preview
    preview := regexp_replace(entry_body, '\*\*([^*]+)\*\*', '\1', 'g'); -- Remove bold
    preview := regexp_replace(preview, '\*([^*]+)\*', '\1', 'g'); -- Remove italic
    preview := regexp_replace(preview, '#+ ', '', 'g'); -- Remove headers
    preview := regexp_replace(preview, '\n+', ' ', 'g'); -- Replace newlines with spaces
    
    IF length(preview) > preview_length THEN
        preview := left(preview, preview_length) || '...';
    END IF;
    
    RETURN preview;
END;
$$ LANGUAGE plpgsql;

-- Fix get_bestsellers function
CREATE OR REPLACE FUNCTION get_bestsellers(book_type_param VARCHAR(10), limit_param INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    title TEXT,
    author_name TEXT,
    author_id UUID,
    cover_image_url TEXT,
    price_amount INTEGER,
    genre TEXT,
    sales_count INTEGER,
    rank INTEGER,
    is_daily_data BOOLEAN
) 
SET search_path = ''
AS $$
DECLARE
    latest_date DATE;
BEGIN
    -- Get the latest date with data
    SELECT MAX(date) INTO latest_date 
    FROM public.daily_bestsellers 
    WHERE book_type = book_type_param;
    
    -- If we have daily data, return it
    IF latest_date IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            db.project_id,
            db.title,
            db.author_name,
            db.author_id,
            db.cover_image_url,
            db.price_amount,
            db.genre,
            db.sales_count,
            db.rank,
            TRUE as is_daily_data
        FROM public.daily_bestsellers db
        WHERE db.date = latest_date 
        AND db.book_type = book_type_param
        ORDER BY db.rank
        LIMIT limit_param;
    ELSE
        -- Fall back to live data
        RETURN QUERY
        SELECT 
            p.id,
            p.title,
            u.name as author_name,
            p.user_id as author_id,
            p.cover_image_url,
            p.price_amount,
            p.genre,
            p.sales_count,
            ROW_NUMBER() OVER (ORDER BY p.sales_count DESC)::INTEGER as rank,
            FALSE as is_daily_data
        FROM public.projects p
        JOIN public.users u ON p.user_id = u.id
        WHERE p.is_complete = TRUE 
        AND p.is_private = FALSE
        AND p.price_amount IS NOT NULL
        AND p.price_amount > 0
        AND CASE 
            WHEN book_type_param = 'ebook' THEN p.is_ebook = TRUE
            WHEN book_type_param = 'audiobook' THEN p.is_audiobook = TRUE
            ELSE TRUE
        END
        ORDER BY p.sales_count DESC
        LIMIT limit_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_audio_reaction_counts function
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
  -- Handle book audio post reactions
  IF NEW.book_audio_post_id IS NOT NULL THEN
    UPDATE public.book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions 
        WHERE book_audio_post_id = NEW.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = NEW.book_audio_post_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_audio_reaction_counts_on_delete function
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts_on_delete()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
  -- Handle book audio post reactions
  IF OLD.book_audio_post_id IS NOT NULL THEN
    UPDATE public.book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions 
        WHERE book_audio_post_id = OLD.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = OLD.book_audio_post_id;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Fix get_creator_subscriber_count function
CREATE OR REPLACE FUNCTION get_creator_subscriber_count(creator_id uuid)
RETURNS INTEGER 
SET search_path = ''
AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER 
        FROM public.subscriptions 
        WHERE writer_id = creator_id 
        AND status = 'active' 
        AND current_period_end > NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Fix calculate_notification_cost function
CREATE OR REPLACE FUNCTION calculate_notification_cost(subscriber_count integer)
RETURNS INTEGER 
SET search_path = ''
AS $$
BEGIN
    -- Cost is 1 credit per subscriber
    RETURN subscriber_count;
END;
$$ LANGUAGE plpgsql;

-- Fix deduct_notification_credits function
CREATE OR REPLACE FUNCTION deduct_notification_credits(user_id uuid, cost integer)
RETURNS BOOLEAN 
SET search_path = ''
AS $$
DECLARE
    current_credits INTEGER;
BEGIN
    -- Get current credits
    SELECT notification_credits INTO current_credits 
    FROM public.users 
    WHERE id = user_id;
    
    -- Check if user has enough credits
    IF current_credits < cost THEN
        RETURN FALSE;
    END IF;
    
    -- Deduct credits
    UPDATE public.users 
    SET notification_credits = notification_credits - cost 
    WHERE id = user_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fix sync_subscriber_reader_id function
CREATE OR REPLACE FUNCTION sync_subscriber_reader_id()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update reader_id to match user_id for consistency
    NEW.reader_id := NEW.user_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add comment reactions support to existing reactions table
-- This extends the reactions table to support book comment reactions with threading

-- Add comment_id column to reactions table
ALTER TABLE reactions ADD COLUMN comment_id UUID REFERENCES comments(id) ON DELETE CASCADE;

-- Add unique constraint for comment reactions (one reaction per user per comment)
CREATE UNIQUE INDEX reactions_comment_user_unique
ON reactions(comment_id, user_id)
WHERE comment_id IS NOT NULL;

-- Add index for comment reactions performance
CREATE INDEX idx_reactions_comment_id ON reactions(comment_id);
CREATE INDEX idx_reactions_comment_user ON reactions(comment_id, user_id);

-- Update the check constraint to include comment reactions
ALTER TABLE reactions DROP CONSTRAINT IF EXISTS reactions_single_content_check;
ALTER TABLE reactions ADD CONSTRAINT reactions_single_content_check
  CHECK (
    (diary_entry_id IS NOT NULL)::int +
    (book_id IS NOT NULL)::int +
    (audio_post_id IS NOT NULL)::int +
    (book_audio_post_id IS NOT NULL)::int +
    (book_audio_reply_id IS NOT NULL)::int +
    (comment_id IS NOT NULL)::int = 1
  );

-- Add RLS policies for comment reactions
CREATE POLICY "Anyone can view comment reactions"
ON reactions
FOR SELECT
USING (comment_id IS NOT NULL);

CREATE POLICY "Users can insert comment reactions"
ON reactions
FOR INSERT
WITH CHECK (comment_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can update their own comment reactions"
ON reactions
FOR UPDATE
USING (comment_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can delete their own comment reactions"
ON reactions
FOR DELETE
USING (comment_id IS NOT NULL AND user_id = auth.uid());

-- Function to get comment reaction counts and user's reaction
CREATE OR REPLACE FUNCTION get_comment_reactions(comment_id_param uuid, user_id_param uuid DEFAULT NULL)
RETURNS TABLE(
    reaction_type text,
    count bigint,
    user_reacted boolean
)
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        r.reaction_type::text,
        COUNT(*)::bigint as count,
        CASE
            WHEN user_id_param IS NOT NULL THEN
                bool_or(r.user_id = user_id_param)
            ELSE false
        END as user_reacted
    FROM public.reactions r
    WHERE r.comment_id = comment_id_param
    GROUP BY r.reaction_type
    ORDER BY count DESC, r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Function to toggle comment reaction (prevents duplicates)
CREATE OR REPLACE FUNCTION toggle_comment_reaction(
    comment_id_param uuid,
    user_id_param uuid,
    reaction_type_param text
)
RETURNS TABLE(
    action text,
    reaction_counts jsonb
)
SET search_path = ''
AS $$
DECLARE
    existing_reaction_type text;
    reaction_counts_result jsonb;
BEGIN
    -- Check if user already has a reaction on this comment
    SELECT reaction_type INTO existing_reaction_type
    FROM public.reactions
    WHERE comment_id = comment_id_param
    AND user_id = user_id_param;

    -- If user has the same reaction, remove it
    IF existing_reaction_type = reaction_type_param THEN
        DELETE FROM public.reactions
        WHERE comment_id = comment_id_param
        AND user_id = user_id_param;

        action := 'removed';
    ELSE
        -- If user has a different reaction, update it
        IF existing_reaction_type IS NOT NULL THEN
            UPDATE public.reactions
            SET reaction_type = reaction_type_param,
                updated_at = NOW()
            WHERE comment_id = comment_id_param
            AND user_id = user_id_param;

            action := 'updated';
        ELSE
            -- If user has no reaction, add it
            INSERT INTO public.reactions (comment_id, user_id, reaction_type)
            VALUES (comment_id_param, user_id_param, reaction_type_param);

            action := 'added';
        END IF;
    END IF;

    -- Get updated reaction counts
    SELECT jsonb_object_agg(reaction_type, count) INTO reaction_counts_result
    FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions
        WHERE comment_id = comment_id_param
        GROUP BY reaction_type
    ) counts;

    reaction_counts := COALESCE(reaction_counts_result, '{}'::jsonb);

    RETURN QUERY SELECT action, reaction_counts;
END;
$$ LANGUAGE plpgsql;

SELECT 'Comment reactions system added successfully' as status;
