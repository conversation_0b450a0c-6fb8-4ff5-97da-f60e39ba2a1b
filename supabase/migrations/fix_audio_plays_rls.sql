-- Fix RLS for audio_plays table
-- This ensures the audio_plays table has proper Row Level Security

-- Enable RLS on audio_plays table
ALTER TABLE audio_plays ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Audio plays are viewable by everyone" ON audio_plays;
DROP POLICY IF EXISTS "Users can create audio plays" ON audio_plays;
DROP POLICY IF EXISTS "Service role can manage audio plays" ON audio_plays;

-- Create RLS policies for audio_plays
-- Allow everyone to view play counts (for public metrics)
CREATE POLICY "Audio plays are viewable by everyone" ON audio_plays
    FOR SELECT USING (true);

-- Allow anyone to create audio plays (including anonymous users)
-- This is needed for tracking plays from non-authenticated users
CREATE POLICY "Users can create audio plays" ON audio_plays
    FOR INSERT WITH CHECK (true);

-- Allow service role to manage audio plays (for admin/system operations)
CREATE POLICY "Service role can manage audio plays" ON audio_plays
    FOR ALL USING (
        current_setting('role') = 'service_role' OR
        current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
    );

-- Ensure audio_achievements also has RLS enabled (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'audio_achievements') THEN
        ALTER TABLE audio_achievements ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Audio achievements are viewable by everyone" ON audio_achievements;
        DROP POLICY IF EXISTS "Service role can manage audio achievements" ON audio_achievements;
        
        -- Create policies for audio_achievements
        CREATE POLICY "Audio achievements are viewable by everyone" ON audio_achievements
            FOR SELECT USING (true);
            
        CREATE POLICY "Service role can manage audio achievements" ON audio_achievements
            FOR ALL USING (
                current_setting('role') = 'service_role' OR
                current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
            );
    END IF;
END $$;

-- Success message
SELECT 'Audio plays RLS policies fixed successfully!' as status;
