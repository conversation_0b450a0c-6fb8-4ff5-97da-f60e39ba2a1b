-- Subscription enforcement helper functions
-- Run this in Supabase SQL editor or via migration

-- Function to update all subscriber counts efficiently
CREATE OR REPLACE FUNCTION update_all_subscriber_counts()
RETURNS void
SET search_path = ''
AS $$
BEGIN
  -- Update subscriber counts for all writers
  -- Use reader_id (not subscriber_id) and check for active subscriptions
  UPDATE public.users
  SET subscriber_count = (
    SELECT COUNT(*)
    FROM public.subscriptions s
    WHERE s.writer_id = users.id
    AND (
      (s.status = 'active' AND COALESCE(s.current_period_end, s.active_until) > NOW()) OR
      (s.status IS NULL AND COALESCE(s.current_period_end, s.active_until) > NOW())
    )
  )
  WHERE role IN ('writer', 'admin');
  
  -- Log the update
  INSERT INTO public.system_logs (event_type, message, created_at)
  VALUES ('subscriber_count_update', 'Updated all subscriber counts', NOW());
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get subscription enforcement stats
CREATE OR REPLACE FUNCTION get_subscription_stats()
RETURNS TABLE(
  total_subscriptions bigint,
  active_subscriptions bigint,
  expired_subscriptions bigint,
  expiring_soon bigint
)
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_subscriptions,
    COUNT(*) FILTER (WHERE
      (status = 'active' OR status IS NULL) AND
      COALESCE(current_period_end, active_until) > NOW()
    ) as active_subscriptions,
    COUNT(*) FILTER (WHERE
      status = 'expired' OR
      COALESCE(current_period_end, active_until) <= NOW()
    ) as expired_subscriptions,
    COUNT(*) FILTER (WHERE
      (status = 'active' OR status IS NULL) AND
      COALESCE(current_period_end, active_until) > NOW() AND
      COALESCE(current_period_end, active_until) <= NOW() + INTERVAL '7 days'
    ) as expiring_soon
  FROM public.subscriptions;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to find subscriptions expiring soon (for notifications)
CREATE OR REPLACE FUNCTION get_expiring_subscriptions(days_ahead integer DEFAULT 3)
RETURNS TABLE(
  subscription_id uuid,
  reader_id uuid,
  writer_id uuid,
  reader_email text,
  writer_name text,
  expires_at timestamptz
)
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id as subscription_id,
    s.reader_id,
    s.writer_id,
    reader.email as reader_email,
    writer.name as writer_name,
    COALESCE(s.current_period_end, s.active_until) as expires_at
  FROM public.subscriptions s
  JOIN public.users reader ON reader.id = s.reader_id
  JOIN public.users writer ON writer.id = s.writer_id
  WHERE (s.status = 'active' OR s.status IS NULL)
    AND COALESCE(s.current_period_end, s.active_until) > NOW()
    AND COALESCE(s.current_period_end, s.active_until) <= NOW() + (days_ahead || ' days')::INTERVAL
  ORDER BY COALESCE(s.current_period_end, s.active_until) ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create system logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.system_logs (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type text NOT NULL,
  message text,
  metadata jsonb,
  created_at timestamptz DEFAULT NOW()
);

-- Enable RLS on system_logs
ALTER TABLE public.system_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view system logs
CREATE POLICY "Admins can view system logs" ON public.system_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Grant permissions
GRANT SELECT ON public.system_logs TO authenticated;
GRANT EXECUTE ON FUNCTION update_all_subscriber_counts() TO authenticated;
GRANT EXECUTE ON FUNCTION get_subscription_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_expiring_subscriptions(integer) TO authenticated;
