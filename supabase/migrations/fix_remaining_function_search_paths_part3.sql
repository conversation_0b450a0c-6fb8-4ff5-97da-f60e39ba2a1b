-- Fix remaining function search_path security warnings - Part 3 (Final)

-- Fix get_bestseller_categories function
CREATE OR REPLACE FUNCTION get_bestseller_categories(book_type_param VARCHAR(10))
RETURNS TABLE(genre TEXT) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT p.genre
    FROM public.projects p
    WHERE p.is_complete = TRUE 
    AND p.is_private = FALSE
    AND p.price_amount IS NOT NULL
    AND p.price_amount > 0
    AND p.genre IS NOT NULL
    AND CASE 
        WHEN book_type_param = 'ebook' THEN p.is_ebook = TRUE
        WHEN book_type_param = 'audiobook' THEN p.is_audiobook = TRUE
        ELSE TRUE
    END
    ORDER BY p.genre;
END;
$$ LANGUAGE plpgsql;

-- Fix get_bestsellers_by_category function
CREATE OR REPLACE FUNCTION get_bestsellers_by_category(
    book_type_param VARCHAR(10), 
    category_param TEXT DEFAULT NULL,
    limit_param INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    author_name TEXT,
    author_id UUID,
    cover_image_url TEXT,
    price_amount INTEGER,
    genre TEXT,
    sales_count INTEGER,
    rank INTEGER,
    is_daily_data BOOLEAN
) 
SET search_path = ''
AS $$
DECLARE
    latest_date DATE;
BEGIN
    -- Get the latest date with data for this category
    SELECT MAX(date) INTO latest_date 
    FROM public.daily_bestsellers 
    WHERE book_type = book_type_param
    AND (category_param IS NULL OR genre = category_param);
    
    -- If we have daily data, return it
    IF latest_date IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            db.project_id,
            db.title,
            db.author_name,
            db.author_id,
            db.cover_image_url,
            db.price_amount,
            db.genre,
            db.sales_count,
            db.rank,
            TRUE as is_daily_data
        FROM public.daily_bestsellers db
        WHERE db.date = latest_date 
        AND db.book_type = book_type_param
        AND (category_param IS NULL OR db.genre = category_param)
        ORDER BY db.rank
        LIMIT limit_param;
    ELSE
        -- Fall back to live data
        RETURN QUERY
        SELECT 
            p.id,
            p.title,
            u.name as author_name,
            p.user_id as author_id,
            p.cover_image_url,
            p.price_amount,
            p.genre,
            p.sales_count,
            ROW_NUMBER() OVER (ORDER BY p.sales_count DESC)::INTEGER as rank,
            FALSE as is_daily_data
        FROM public.projects p
        JOIN public.users u ON p.user_id = u.id
        WHERE p.is_complete = TRUE 
        AND p.is_private = FALSE
        AND p.price_amount IS NOT NULL
        AND p.price_amount > 0
        AND (category_param IS NULL OR p.genre = category_param)
        AND CASE 
            WHEN book_type_param = 'ebook' THEN p.is_ebook = TRUE
            WHEN book_type_param = 'audiobook' THEN p.is_audiobook = TRUE
            ELSE TRUE
        END
        ORDER BY p.sales_count DESC
        LIMIT limit_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Fix update_entry_keywords function
CREATE OR REPLACE FUNCTION update_entry_keywords()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Extract keywords from title and body for search optimization
    NEW.keywords := to_tsvector('english', COALESCE(NEW.title, '') || ' ' || COALESCE(NEW.body_md, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_daily_bestsellers function
CREATE OR REPLACE FUNCTION update_daily_bestsellers(target_date DATE DEFAULT CURRENT_DATE)
RETURNS VOID 
SET search_path = ''
AS $$
DECLARE
    paid_book RECORD;
    current_rank INTEGER;
BEGIN
    -- Clear existing data for the target date
    DELETE FROM public.daily_bestsellers WHERE date = target_date;
    
    -- Insert ebook bestsellers
    current_rank := 1;
    FOR paid_book IN 
        SELECT 
            p.id, p.title, p.user_id, u.name as author_name,
            p.cover_image_url, p.price_amount, p.genre, p.sales_count
        FROM public.projects p
        JOIN public.users u ON p.user_id = u.id
        WHERE p.is_ebook = TRUE 
        AND p.is_complete = TRUE 
        AND p.is_private = FALSE
        AND p.price_amount IS NOT NULL 
        AND p.price_amount > 0
        ORDER BY p.sales_count DESC
        LIMIT 100
    LOOP
        INSERT INTO public.daily_bestsellers (
            date, book_type, project_id, title, author_id, author_name,
            cover_image_url, price_amount, genre, sales_count, rank
        ) VALUES (
            target_date, 'ebook', paid_book.id, paid_book.title, 
            paid_book.user_id, paid_book.author_name,
            paid_book.cover_image_url, paid_book.price_amount, paid_book.genre,
            paid_book.sales_count, current_rank
        );
        current_rank := current_rank + 1;
    END LOOP;
    
    -- Insert audiobook bestsellers
    current_rank := 1;
    FOR paid_book IN 
        SELECT 
            p.id, p.title, p.user_id, u.name as author_name,
            p.cover_image_url, p.price_amount, p.genre, p.sales_count
        FROM public.projects p
        JOIN public.users u ON p.user_id = u.id
        WHERE p.is_audiobook = TRUE 
        AND p.is_complete = TRUE 
        AND p.is_private = FALSE
        AND p.price_amount IS NOT NULL 
        AND p.price_amount > 0
        ORDER BY p.sales_count DESC
        LIMIT 100
    LOOP
        INSERT INTO public.daily_bestsellers (
            date, book_type, project_id, title, author_id, author_name,
            cover_image_url, price_amount, genre, sales_count, rank
        ) VALUES (
            target_date, 'audiobook', paid_book.id, paid_book.title, 
            paid_book.user_id, paid_book.author_name,
            paid_book.cover_image_url, paid_book.price_amount, paid_book.genre,
            paid_book.sales_count, current_rank
        );
        current_rank := current_rank + 1;
    END LOOP;
    
    RAISE NOTICE 'Daily bestsellers updated for date: %', target_date;
END;
$$ LANGUAGE plpgsql;

SELECT 'All remaining function search_path fixes applied successfully - Part 3 (Final)' as status;
