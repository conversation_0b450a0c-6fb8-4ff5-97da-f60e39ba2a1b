-- Fix remaining function search_path security warnings
-- Part 3: Final batch of functions that need SET search_path = ''

-- Fix get_story_venture_stats function
CREATE OR REPLACE FUNCTION get_story_venture_stats(entry_id uuid)
RETURNS TABLE(
    total_backers bigint,
    total_amount numeric,
    goal_amount numeric,
    is_goal_met boolean
) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::bigint as total_backers,
        COALESCE(SUM(amount), 0)::numeric as total_amount,
        de.story_venture_goal::numeric as goal_amount,
        (COALESCE(SUM(amount), 0) >= de.story_venture_goal) as is_goal_met
    FROM public.story_venture_contributions svc
    RIGHT JOIN public.diary_entries de ON de.id = entry_id
    WHERE svc.diary_entry_id = entry_id OR svc.diary_entry_id IS NULL
    GROUP BY de.story_venture_goal;
END;
$$ LANGUAGE plpgsql;

-- Fix get_public_story_venture_backers function
CREATE OR REPLACE FUNCTION get_public_story_venture_backers(entry_id uuid, limit_count integer DEFAULT 10)
RETURNS TABLE(
    backer_name text,
    backer_avatar text,
    amount numeric,
    message text,
    created_at timestamp with time zone
) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.name as backer_name,
        u.avatar as backer_avatar,
        svc.amount,
        svc.message,
        svc.created_at
    FROM public.story_venture_contributions svc
    JOIN public.users u ON svc.user_id = u.id
    WHERE svc.diary_entry_id = entry_id
    AND svc.is_anonymous = FALSE
    ORDER BY svc.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Fix get_entry_preview function
CREATE OR REPLACE FUNCTION get_entry_preview(entry_id uuid, preview_length integer DEFAULT 200)
RETURNS TEXT 
SET search_path = ''
AS $$
DECLARE
    entry_body TEXT;
    preview TEXT;
BEGIN
    SELECT body_md INTO entry_body 
    FROM public.diary_entries 
    WHERE id = entry_id;
    
    IF entry_body IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Remove markdown formatting and get preview
    preview := regexp_replace(entry_body, '\*\*([^*]+)\*\*', '\1', 'g'); -- Remove bold
    preview := regexp_replace(preview, '\*([^*]+)\*', '\1', 'g'); -- Remove italic
    preview := regexp_replace(preview, '#+ ', '', 'g'); -- Remove headers
    preview := regexp_replace(preview, '\n+', ' ', 'g'); -- Replace newlines with spaces
    
    IF length(preview) > preview_length THEN
        preview := left(preview, preview_length) || '...';
    END IF;
    
    RETURN preview;
END;
$$ LANGUAGE plpgsql;

-- Fix get_creator_subscriber_count function
CREATE OR REPLACE FUNCTION get_creator_subscriber_count(creator_id uuid)
RETURNS INTEGER 
SET search_path = ''
AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER 
        FROM public.subscriptions 
        WHERE writer_id = creator_id 
        AND status = 'active' 
        AND current_period_end > NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- Fix calculate_notification_cost function
CREATE OR REPLACE FUNCTION calculate_notification_cost(subscriber_count integer)
RETURNS INTEGER 
SET search_path = ''
AS $$
BEGIN
    -- Cost is 1 credit per subscriber
    RETURN subscriber_count;
END;
$$ LANGUAGE plpgsql;

-- Fix deduct_notification_credits function
CREATE OR REPLACE FUNCTION deduct_notification_credits(user_id uuid, cost integer)
RETURNS BOOLEAN 
SET search_path = ''
AS $$
DECLARE
    current_credits INTEGER;
BEGIN
    -- Get current credits
    SELECT notification_credits INTO current_credits 
    FROM public.users 
    WHERE id = user_id;
    
    -- Check if user has enough credits
    IF current_credits < cost THEN
        RETURN FALSE;
    END IF;
    
    -- Deduct credits
    UPDATE public.users 
    SET notification_credits = notification_credits - cost 
    WHERE id = user_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fix sync_subscriber_reader_id function
CREATE OR REPLACE FUNCTION sync_subscriber_reader_id()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update reader_id to match user_id for consistency
    NEW.reader_id := NEW.user_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_entry_count function
CREATE OR REPLACE FUNCTION update_entry_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.users 
        SET entry_count = entry_count + 1 
        WHERE id = NEW.user_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.users 
        SET entry_count = GREATEST(entry_count - 1, 0) 
        WHERE id = OLD.user_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix check_photo_limit function
CREATE OR REPLACE FUNCTION check_photo_limit()
RETURNS TRIGGER 
SET search_path = ''
AS $$
DECLARE
    photo_count INTEGER;
BEGIN
    -- Count existing photos for this diary entry
    SELECT COUNT(*) INTO photo_count 
    FROM public.photos 
    WHERE diary_entry_id = NEW.diary_entry_id;
    
    -- Allow maximum of 5 photos per entry
    IF photo_count >= 5 THEN
        RAISE EXCEPTION 'Maximum of 5 photos allowed per diary entry';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_rating function
CREATE OR REPLACE FUNCTION update_book_rating()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update the project's average rating and review count
    UPDATE public.projects 
    SET 
        average_rating = (
            SELECT AVG(rating)::DECIMAL(3,2) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

SELECT 'Function search_path fixes part 3 applied successfully' as status;
