-- Fix RLS permissions for Code Book automation
-- This allows the changelog function to bypass <PERSON><PERSON> and create entries

-- Drop and recreate the function with SECURITY DEFINER
DROP FUNCTION IF EXISTS create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE);

CREATE OR REPLACE FUNCTION create_changelog_from_commit(
    p_commit_hash TEXT,
    p_commit_message TEXT,
    p_commit_author TEXT DEFAULT 'OnlyDiary Team',
    p_commit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS UUID 
LANGUAGE plpgsql
SECURITY DEFINER  -- This allows the function to bypass RLS
SET search_path = public
AS $$
DECLARE
    changelog_user_id UUID;
    new_entry_id UUID;
    entry_title TEXT;
    entry_content TEXT;
    existing_commit TEXT;
BEGIN
    -- Check if this commit was already processed
    SELECT cc.commit_hash INTO existing_commit 
    FROM changelog_commits cc
    WHERE cc.commit_hash = p_commit_hash;
    
    IF existing_commit IS NOT NULL THEN
        RAISE NOTICE 'Commit % already processed, skipping', p_commit_hash;
        RETURN NULL;
    END IF;
    
    -- Get the ChangeLog user ID
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    IF changelog_user_id IS NULL THEN
        RAISE EXCEPTION 'ChangeLog user not found. Please run the setup migration first.';
    END IF;
    
    -- Generate user-friendly title from commit message
    entry_title := 'Platform Update - ' || TO_CHAR(p_commit_date, 'Month DD, YYYY');
    
    -- Generate user-friendly content
    entry_content := '# ' || entry_title || E'\n\n';
    entry_content := entry_content || 'Hey everyone! 👋' || E'\n\n';
    entry_content := entry_content || 'Just pushed an update to make OnlyDiary better:' || E'\n\n';
    entry_content := entry_content || '## What Changed' || E'\n\n';
    entry_content := entry_content || '✨ ' || p_commit_message || E'\n\n';
    entry_content := entry_content || '## Technical Details' || E'\n\n';
    entry_content := entry_content || '- **Author**: ' || p_commit_author || E'\n';
    entry_content := entry_content || '- **Commit**: `' || LEFT(p_commit_hash, 8) || '`' || E'\n';
    entry_content := entry_content || '- **Date**: ' || TO_CHAR(p_commit_date, 'YYYY-MM-DD HH24:MI:SS TZ') || E'\n\n';
    entry_content := entry_content || 'These improvements are live now! No action needed on your part.' || E'\n\n';
    entry_content := entry_content || '---' || E'\n';
    entry_content := entry_content || '*Have feedback or suggestions? Feel free to reach out!*';
    
    -- Create the new entry (SECURITY DEFINER allows this to bypass RLS)
    INSERT INTO diary_entries (
        user_id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        entry_title,
        entry_content,
        true, -- Always free
        false, -- Not hidden
        p_commit_date,
        NOW()
    ) RETURNING id INTO new_entry_id;
    
    -- Record that we processed this commit
    INSERT INTO changelog_commits (
        commit_hash,
        commit_message,
        entry_id,
        processed_at
    ) VALUES (
        p_commit_hash,
        p_commit_message,
        new_entry_id,
        NOW()
    );
    
    RAISE NOTICE 'Created changelog entry % for commit %', new_entry_id, p_commit_hash;
    
    RETURN new_entry_id;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) TO anon;

-- Verify the fix
SELECT 'Changelog RLS permissions fixed successfully!' as status;
