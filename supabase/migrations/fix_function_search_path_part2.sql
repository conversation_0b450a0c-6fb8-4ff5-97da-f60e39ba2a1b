-- Fix remaining function search_path security warnings
-- Part 2: Additional functions that need SET search_path = '' (no duplicates from part 1)

-- Fix update_book_audio_reaction_counts function
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
  -- Handle book audio post reactions
  IF NEW.book_audio_post_id IS NOT NULL THEN
    UPDATE public.book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions 
        WHERE book_audio_post_id = NEW.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = NEW.book_audio_post_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_audio_reaction_counts_on_delete function
CREATE OR REPLACE FUNCTION update_book_audio_reaction_counts_on_delete()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
  -- Handle book audio post reactions
  IF OLD.book_audio_post_id IS NOT NULL THEN
    UPDATE public.book_audio_posts 
    SET reaction_counts = (
      SELECT COALESCE(jsonb_object_agg(reaction_type, count), '{}')
      FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions 
        WHERE book_audio_post_id = OLD.book_audio_post_id
        GROUP BY reaction_type
      ) counts
    )
    WHERE id = OLD.book_audio_post_id;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Fix get_bestsellers function
CREATE OR REPLACE FUNCTION get_bestsellers(book_type_param VARCHAR(10), limit_param INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    title TEXT,
    author_name TEXT,
    author_id UUID,
    cover_image_url TEXT,
    price_amount INTEGER,
    genre TEXT,
    sales_count INTEGER,
    rank INTEGER,
    is_daily_data BOOLEAN
) 
SET search_path = ''
AS $$
DECLARE
    latest_date DATE;
BEGIN
    -- Get the latest date with data
    SELECT MAX(date) INTO latest_date 
    FROM public.daily_bestsellers 
    WHERE book_type = book_type_param;
    
    -- If we have daily data, return it
    IF latest_date IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            db.project_id,
            db.title,
            db.author_name,
            db.author_id,
            db.cover_image_url,
            db.price_amount,
            db.genre,
            db.sales_count,
            db.rank,
            TRUE as is_daily_data
        FROM public.daily_bestsellers db
        WHERE db.date = latest_date 
        AND db.book_type = book_type_param
        ORDER BY db.rank
        LIMIT limit_param;
    ELSE
        -- Fall back to live data
        RETURN QUERY
        SELECT 
            p.id,
            p.title,
            u.name as author_name,
            p.user_id as author_id,
            p.cover_image_url,
            p.price_amount,
            p.genre,
            p.sales_count,
            ROW_NUMBER() OVER (ORDER BY p.sales_count DESC)::INTEGER as rank,
            FALSE as is_daily_data
        FROM public.projects p
        JOIN public.users u ON p.user_id = u.id
        WHERE p.is_complete = TRUE 
        AND p.is_private = FALSE
        AND p.price_amount IS NOT NULL
        AND p.price_amount > 0
        AND CASE 
            WHEN book_type_param = 'ebook' THEN p.is_ebook = TRUE
            WHEN book_type_param = 'audiobook' THEN p.is_audiobook = TRUE
            ELSE TRUE
        END
        ORDER BY p.sales_count DESC
        LIMIT limit_param;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Fix get_audio_reaction_counts function
CREATE OR REPLACE FUNCTION get_audio_reaction_counts(post_id uuid)
RETURNS TABLE(reaction_type text, count bigint) 
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.reaction_type::text,
    COUNT(*)::bigint
  FROM public.reactions r
  WHERE r.audio_post_id = post_id
  GROUP BY r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_withdrawals_updated_at function
CREATE OR REPLACE FUNCTION update_withdrawals_updated_at()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

SELECT 'Function search_path fixes part 2 applied successfully' as status;
