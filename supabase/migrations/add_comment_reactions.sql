-- Add comment reactions support to existing reactions table
-- This extends the reactions table to support book comment reactions with threading

-- Fix book_audio_replies foreign key to reference users table instead of auth.users
ALTER TABLE book_audio_replies DROP CONSTRAINT IF EXISTS book_audio_replies_user_id_fkey;
ALTER TABLE book_audio_replies ADD CONSTRAINT book_audio_replies_user_id_fkey
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Fix book_audio_posts foreign key to reference users table instead of auth.users
ALTER TABLE book_audio_posts DROP CONSTRAINT IF EXISTS book_audio_posts_user_id_fkey;
ALTER TABLE book_audio_posts ADD CONSTRAINT book_audio_posts_user_id_fkey
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add comment_id column to reactions table
ALTER TABLE reactions ADD COLUMN comment_id UUID REFERENCES comments(id) ON DELETE CASCADE;

-- Add unique constraint for comment reactions (one reaction per user per comment)
CREATE UNIQUE INDEX reactions_comment_user_unique 
ON reactions(comment_id, user_id) 
WHERE comment_id IS NOT NULL;

-- Add indexes for comment reactions performance
CREATE INDEX idx_reactions_comment_id ON reactions(comment_id);
CREATE INDEX idx_reactions_comment_user ON reactions(comment_id, user_id);

-- Update the check constraint to include comment reactions
ALTER TABLE reactions DROP CONSTRAINT IF EXISTS reactions_single_content_check;
ALTER TABLE reactions ADD CONSTRAINT reactions_single_content_check 
  CHECK (
    (diary_entry_id IS NOT NULL)::int + 
    (book_id IS NOT NULL)::int + 
    (audio_post_id IS NOT NULL)::int + 
    (book_audio_post_id IS NOT NULL)::int + 
    (book_audio_reply_id IS NOT NULL)::int + 
    (comment_id IS NOT NULL)::int = 1
  );

-- Add RLS policies for comment reactions
CREATE POLICY "Anyone can view comment reactions"
ON reactions
FOR SELECT 
USING (comment_id IS NOT NULL);

CREATE POLICY "Users can insert comment reactions"
ON reactions
FOR INSERT
WITH CHECK (comment_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can update their own comment reactions"
ON reactions
FOR UPDATE
USING (comment_id IS NOT NULL AND user_id = auth.uid());

CREATE POLICY "Users can delete their own comment reactions"
ON reactions
FOR DELETE
USING (comment_id IS NOT NULL AND user_id = auth.uid());

-- Function to get comment reaction counts and user's reaction
CREATE OR REPLACE FUNCTION get_comment_reactions(comment_id_param uuid, user_id_param uuid DEFAULT NULL)
RETURNS TABLE(
    reaction_type text,
    count bigint,
    user_reacted boolean
) 
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.reaction_type::text,
        COUNT(*)::bigint as count,
        CASE 
            WHEN user_id_param IS NOT NULL THEN 
                bool_or(r.user_id = user_id_param)
            ELSE false
        END as user_reacted
    FROM public.reactions r
    WHERE r.comment_id = comment_id_param
    GROUP BY r.reaction_type
    ORDER BY count DESC, r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Function to toggle comment reaction (prevents duplicates)
CREATE OR REPLACE FUNCTION toggle_comment_reaction(
    comment_id_param uuid,
    user_id_param uuid,
    reaction_type_param text
)
RETURNS TABLE(
    action text,
    reaction_counts jsonb
) 
SET search_path = ''
AS $$
DECLARE
    existing_reaction_type text;
    reaction_counts_result jsonb;
BEGIN
    -- Check if user already has a reaction on this comment
    SELECT reaction_type INTO existing_reaction_type
    FROM public.reactions 
    WHERE comment_id = comment_id_param 
    AND user_id = user_id_param;
    
    -- If user has the same reaction, remove it
    IF existing_reaction_type = reaction_type_param THEN
        DELETE FROM public.reactions 
        WHERE comment_id = comment_id_param 
        AND user_id = user_id_param;
        
        action := 'removed';
    ELSE
        -- If user has a different reaction, update it
        IF existing_reaction_type IS NOT NULL THEN
            UPDATE public.reactions 
            SET reaction_type = reaction_type_param,
                updated_at = NOW()
            WHERE comment_id = comment_id_param 
            AND user_id = user_id_param;
            
            action := 'updated';
        ELSE
            -- If user has no reaction, add it
            INSERT INTO public.reactions (comment_id, user_id, reaction_type)
            VALUES (comment_id_param, user_id_param, reaction_type_param);
            
            action := 'added';
        END IF;
    END IF;
    
    -- Get updated reaction counts
    SELECT jsonb_object_agg(reaction_type, count) INTO reaction_counts_result
    FROM (
        SELECT reaction_type, COUNT(*)::int as count
        FROM public.reactions 
        WHERE comment_id = comment_id_param
        GROUP BY reaction_type
    ) counts;
    
    reaction_counts := COALESCE(reaction_counts_result, '{}'::jsonb);
    
    RETURN QUERY SELECT action, reaction_counts;
END;
$$ LANGUAGE plpgsql;

SELECT 'Comment reactions system added successfully' as status;
