-- Fix remaining function search_path security warnings - Part 2

-- Fix update_entry_count function
CREATE OR REPLACE FUNCTION update_entry_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.users 
        SET entry_count = entry_count + 1 
        WHERE id = NEW.user_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.users 
        SET entry_count = GREATEST(entry_count - 1, 0) 
        WHERE id = OLD.user_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix check_photo_limit function
CREATE OR REPLACE FUNCTION check_photo_limit()
RETURNS TRIGGER 
SET search_path = ''
AS $$
DECLARE
    photo_count INTEGER;
BEGIN
    -- Count existing photos for this diary entry
    SELECT COUNT(*) INTO photo_count 
    FROM public.photos 
    WHERE diary_entry_id = NEW.diary_entry_id;
    
    -- Allow maximum of 5 photos per entry
    IF photo_count >= 5 THEN
        RAISE EXCEPTION 'Maximum of 5 photos allowed per diary entry';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_rating function
CREATE OR REPLACE FUNCTION update_book_rating()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update the project's average rating and review count
    UPDATE public.projects 
    SET 
        average_rating = (
            SELECT AVG(rating)::DECIMAL(3,2) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        ),
        review_count = (
            SELECT COUNT(*) 
            FROM public.book_reviews 
            WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
        )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Fix create_timeline_post_for_diary function
CREATE OR REPLACE FUNCTION create_timeline_post_for_diary()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Only create timeline post for non-hidden entries
    IF NEW.is_hidden = FALSE THEN
        INSERT INTO public.timeline_posts (
            user_id,
            content_type,
            content_id,
            title,
            preview_text,
            created_at
        ) VALUES (
            NEW.user_id,
            'diary_entry',
            NEW.id,
            NEW.title,
            LEFT(NEW.body_md, 200),
            NEW.created_at
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix create_timeline_post_for_book function
CREATE OR REPLACE FUNCTION create_timeline_post_for_book()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Only create timeline post for public, complete books
    IF NEW.is_private = FALSE AND NEW.is_complete = TRUE THEN
        INSERT INTO public.timeline_posts (
            user_id,
            content_type,
            content_id,
            title,
            preview_text,
            created_at
        ) VALUES (
            NEW.user_id,
            'book_project',
            NEW.id,
            NEW.title,
            LEFT(NEW.description, 200),
            NEW.created_at
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix create_timeline_post_for_voice function
CREATE OR REPLACE FUNCTION create_timeline_post_for_voice()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    INSERT INTO public.timeline_posts (
        user_id,
        content_type,
        content_id,
        title,
        preview_text,
        created_at
    ) VALUES (
        NEW.user_id,
        'audio_post',
        NEW.id,
        COALESCE(NEW.title, 'Voice Post'),
        LEFT(COALESCE(NEW.text_content, ''), 50),
        NEW.created_at
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_view_count function
CREATE OR REPLACE FUNCTION update_view_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update diary entry view count
        IF NEW.diary_entry_id IS NOT NULL THEN
            UPDATE public.diary_entries 
            SET view_count = view_count + 1 
            WHERE id = NEW.diary_entry_id;
        END IF;
        
        -- Update project view count
        IF NEW.project_id IS NOT NULL THEN
            UPDATE public.projects 
            SET view_count = view_count + 1 
            WHERE id = NEW.project_id;
        END IF;
        
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_follower_count function
CREATE OR REPLACE FUNCTION update_follower_count()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.users
        SET follower_count = follower_count + 1
        WHERE id = NEW.writer_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.users
        SET follower_count = GREATEST(follower_count - 1, 0)
        WHERE id = OLD.writer_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update follower count when follows are added/removed
DROP TRIGGER IF EXISTS update_follower_count_trigger ON follows;
CREATE TRIGGER update_follower_count_trigger
    AFTER INSERT OR DELETE ON follows
    FOR EACH ROW EXECUTE FUNCTION update_follower_count();

-- Fix sync_sales_counts function
CREATE OR REPLACE FUNCTION sync_sales_counts()
RETURNS VOID 
SET search_path = ''
AS $$
BEGIN
    -- Update sales counts from purchases table
    UPDATE public.projects 
    SET sales_count = (
        SELECT COUNT(*) 
        FROM public.purchases p 
        WHERE p.project_id = projects.id 
        AND p.status = 'completed'
    );
END;
$$ LANGUAGE plpgsql;

-- Fix update_project_stats function
CREATE OR REPLACE FUNCTION update_project_stats()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.status = 'completed' THEN
        -- Increment sales count
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
        -- Increment sales count when status changes to completed
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' AND OLD.status = 'completed' AND NEW.status != 'completed' THEN
        -- Decrement sales count when status changes from completed
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' AND OLD.status = 'completed' THEN
        -- Decrement sales count when completed purchase is deleted
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = OLD.project_id;
        
        RETURN OLD;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Fix get_audio_reaction_counts function
CREATE OR REPLACE FUNCTION get_audio_reaction_counts(post_id uuid)
RETURNS TABLE(reaction_type text, count bigint) 
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.reaction_type::text,
    COUNT(*)::bigint
  FROM public.reactions r
  WHERE r.audio_post_id = post_id
  GROUP BY r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Fix reset_for_launch function
CREATE OR REPLACE FUNCTION reset_for_launch()
RETURNS VOID 
SET search_path = ''
AS $$
BEGIN
    -- Reset all user signup numbers and badges for launch
    UPDATE public.users 
    SET 
        signup_number = NULL,
        has_day1_badge = FALSE,
        badge_tier = NULL;
    
    -- Reset any other launch-related data
    DELETE FROM public.daily_bestsellers;
    
    RAISE NOTICE 'Database reset for launch completed';
END;
$$ LANGUAGE plpgsql;

-- Fix update_project_sales_count function
CREATE OR REPLACE FUNCTION update_project_sales_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.projects 
        SET sales_count = sales_count + 1 
        WHERE id = NEW.project_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.projects 
        SET sales_count = GREATEST(sales_count - 1, 0) 
        WHERE id = OLD.project_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

SELECT 'Remaining function search_path fixes applied successfully - Part 2' as status;
