-- Fix Supabase Security Linter Errors
-- This addresses the RLS and Security Definer issues

-- 1. Enable RLS on highlights table
ALTER TABLE public.highlights ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for highlights
CREATE POLICY "Users can view highlights on content they have access to" ON public.highlights
  FOR SELECT USING (
    -- Allow if the highlighted content is accessible to the user
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = highlights.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.reader_id = auth.uid()
          AND s.status = 'active'
          AND s.current_period_end > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can create highlights on accessible content" ON public.highlights
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND user_id = auth.uid()
    AND EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = highlights.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.reader_id = auth.uid()
          AND s.status = 'active'
          AND s.current_period_end > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can update their own highlights" ON public.highlights
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own highlights" ON public.highlights
  FOR DELETE USING (user_id = auth.uid());

-- 2. Enable RLS on margin_comments table
ALTER TABLE public.margin_comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for margin_comments
CREATE POLICY "Users can view margin comments on accessible content" ON public.margin_comments
  FOR SELECT USING (
    -- Allow if the commented content is accessible to the user
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = margin_comments.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.reader_id = auth.uid()
          AND s.status = 'active'
          AND s.current_period_end > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can create margin comments on accessible content" ON public.margin_comments
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND user_id = auth.uid()
    AND EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = margin_comments.project_id
      AND (
        p.is_private = false
        OR p.user_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM subscriptions s
          WHERE s.writer_id = p.user_id
          AND s.reader_id = auth.uid()
          AND s.status = 'active'
          AND s.current_period_end > NOW()
        )
      )
    )
  );

CREATE POLICY "Users can update their own margin comments" ON public.margin_comments
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own margin comments" ON public.margin_comments
  FOR DELETE USING (user_id = auth.uid());

-- 3. Enable RLS on passage_popularity table
ALTER TABLE public.passage_popularity ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for passage_popularity
CREATE POLICY "Anyone can view passage popularity" ON public.passage_popularity
  FOR SELECT USING (true);

CREATE POLICY "System can update passage popularity" ON public.passage_popularity
  FOR ALL USING (true);

-- 4. Fix the security definer view issue
-- Drop and recreate the user_conversations view without SECURITY DEFINER
DROP VIEW IF EXISTS public.user_conversations;

-- Recreate without SECURITY DEFINER (uses SECURITY INVOKER by default)
CREATE VIEW public.user_conversations AS
SELECT DISTINCT
  CASE 
    WHEN c.user_id < de.user_id THEN c.user_id 
    ELSE de.user_id 
  END as user1_id,
  CASE 
    WHEN c.user_id < de.user_id THEN de.user_id 
    ELSE c.user_id 
  END as user2_id,
  MAX(c.created_at) as last_message_at,
  COUNT(*) as message_count
FROM comments c
JOIN diary_entries de ON c.diary_entry_id = de.id
WHERE c.user_id != de.user_id  -- Only conversations between different users
GROUP BY 
  CASE 
    WHEN c.user_id < de.user_id THEN c.user_id 
    ELSE de.user_id 
  END,
  CASE 
    WHEN c.user_id < de.user_id THEN de.user_id 
    ELSE c.user_id 
  END;

-- Grant appropriate permissions
GRANT SELECT ON public.user_conversations TO authenticated;

-- 5. Fix function search_path security warnings
-- Add SET search_path = '' to all functions to prevent schema injection attacks

-- Fix get_reaction_counts function
CREATE OR REPLACE FUNCTION get_reaction_counts(entry_id uuid)
RETURNS TABLE(reaction_type text, count bigint)
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.reaction_type::text,
    COUNT(*)::bigint
  FROM public.reactions r
  WHERE r.diary_entry_id = entry_id
  GROUP BY r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Fix update_audio_love_count function
CREATE OR REPLACE FUNCTION update_audio_love_count()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.audio_post_id IS NOT NULL THEN
            UPDATE public.audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.audio_post_id;
        ELSIF NEW.audio_reply_id IS NOT NULL THEN
            UPDATE public.audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.audio_post_id IS NOT NULL THEN
            UPDATE public.audio_posts
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_post_id;
        ELSIF OLD.audio_reply_id IS NOT NULL THEN
            UPDATE public.audio_replies
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_audio_reply_count function
CREATE OR REPLACE FUNCTION update_audio_reply_count()
RETURNS TRIGGER
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.audio_posts
        SET reply_count = reply_count + 1
        WHERE id = NEW.audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.audio_posts
        SET reply_count = GREATEST(reply_count - 1, 0)
        WHERE id = OLD.audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix assign_day1_badge function
CREATE OR REPLACE FUNCTION assign_day1_badge()
RETURNS TRIGGER
SET search_path = ''
AS $$
DECLARE
    current_signup_count INTEGER;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM public.users WHERE id != NEW.id;

    -- Assign signup number (1-based)
    NEW.signup_number := current_signup_count + 1;

    -- Assign badge tier based on signup number
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'day1';
    ELSIF current_signup_count < 1000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'pioneer';
    ELSIF current_signup_count < 2500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'founder';
    ELSIF current_signup_count < 5000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'early_adopter';
    ELSIF current_signup_count < 10000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'charter_member';
    ELSE
        NEW.has_day1_badge := FALSE;
        NEW.badge_tier := NULL;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix get_day1_badge_holders function
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    badge_tier TEXT,
    created_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.badge_tier,
        u.created_at
    FROM public.users u
    WHERE u.has_day1_badge = TRUE
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix get_day1_badge_count function
CREATE OR REPLACE FUNCTION get_day1_badge_count()
RETURNS INTEGER
SET search_path = ''
AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM public.users WHERE has_day1_badge = TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Verify the fixes
SELECT 'Security fixes applied successfully' as status;
