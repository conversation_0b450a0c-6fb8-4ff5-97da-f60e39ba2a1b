-- Enhanced Timeline Algorithm & Engagement Tracking
-- This migration adds comprehensive engagement tracking for the timeline algorithm

-- 1. Create engagement_metrics table for tracking post performance
CREATE TABLE IF NOT EXISTS engagement_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('diary', 'audio', 'book')),
    content_id UUID NOT NULL,
    
    -- Click-through tracking
    impressions INTEGER DEFAULT 0, -- How many times shown in timeline
    clicks INTEGER DEFAULT 0, -- How many times clicked/opened
    click_through_rate DECIMAL(5,4) DEFAULT 0, -- CTR percentage (0.0000 to 1.0000)
    
    -- Interaction tracking
    reactions_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    
    -- Engagement rate calculation
    engagement_rate DECIMAL(5,4) DEFAULT 0, -- (reactions + comments + shares) / impressions
    
    -- Trending score (combines CTR and engagement)
    trending_score DECIMAL(8,4) DEFAULT 0,
    
    -- Time-based metrics
    last_impression_at TIMESTAMP WITH TIME ZONE,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one record per content item
    UNIQUE(content_type, content_id)
);

-- 2. Create post_impressions table for tracking individual impressions
CREATE TABLE IF NOT EXISTS post_impressions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- NULL for anonymous
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('diary', 'audio', 'book')),
    content_id UUID NOT NULL,
    ip_address INET,
    user_agent TEXT,
    source VARCHAR(50) DEFAULT 'timeline', -- 'timeline', 'profile', 'search', 'recommended'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate impressions within short timeframe
    UNIQUE(user_id, content_type, content_id, created_at::date),
    UNIQUE(ip_address, content_type, content_id, created_at::date)
);

-- 3. Create post_clicks table for tracking clicks
CREATE TABLE IF NOT EXISTS post_clicks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- NULL for anonymous
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('diary', 'audio', 'book')),
    content_id UUID NOT NULL,
    source VARCHAR(50) DEFAULT 'timeline', -- Where they clicked from
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create share_tracking table
CREATE TABLE IF NOT EXISTS share_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(20) NOT NULL CHECK (content_type IN ('diary', 'audio', 'book')),
    content_id UUID NOT NULL,
    platform VARCHAR(50) NOT NULL, -- 'twitter', 'facebook', 'linkedin', 'copy', 'native'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add indexes for performance
CREATE INDEX idx_engagement_metrics_trending_score ON engagement_metrics(trending_score DESC);
CREATE INDEX idx_engagement_metrics_content ON engagement_metrics(content_type, content_id);
CREATE INDEX idx_engagement_metrics_updated_at ON engagement_metrics(updated_at);

CREATE INDEX idx_post_impressions_content ON post_impressions(content_type, content_id);
CREATE INDEX idx_post_impressions_user ON post_impressions(user_id);
CREATE INDEX idx_post_impressions_created_at ON post_impressions(created_at);

CREATE INDEX idx_post_clicks_content ON post_clicks(content_type, content_id);
CREATE INDEX idx_post_clicks_user ON post_clicks(user_id);
CREATE INDEX idx_post_clicks_created_at ON post_clicks(created_at);

CREATE INDEX idx_share_tracking_content ON share_tracking(content_type, content_id);
CREATE INDEX idx_share_tracking_platform ON share_tracking(platform);

-- 6. Enable RLS on new tables
ALTER TABLE engagement_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_impressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE share_tracking ENABLE ROW LEVEL SECURITY;

-- 7. RLS Policies
CREATE POLICY "Anyone can view engagement metrics" ON engagement_metrics FOR SELECT USING (true);
CREATE POLICY "System can manage engagement metrics" ON engagement_metrics FOR ALL USING (true);

CREATE POLICY "Anyone can insert impressions" ON post_impressions FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can view impressions" ON post_impressions FOR SELECT USING (true);

CREATE POLICY "Anyone can insert clicks" ON post_clicks FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can view clicks" ON post_clicks FOR SELECT USING (true);

CREATE POLICY "Users can track their shares" ON share_tracking FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Anyone can view share tracking" ON share_tracking FOR SELECT USING (true);

-- 8. Function to update engagement metrics
CREATE OR REPLACE FUNCTION update_engagement_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle impressions
    IF TG_TABLE_NAME = 'post_impressions' AND TG_OP = 'INSERT' THEN
        INSERT INTO engagement_metrics (content_type, content_id, impressions, last_impression_at, updated_at)
        VALUES (NEW.content_type, NEW.content_id, 1, NEW.created_at, NOW())
        ON CONFLICT (content_type, content_id)
        DO UPDATE SET
            impressions = engagement_metrics.impressions + 1,
            last_impression_at = NEW.created_at,
            updated_at = NOW(),
            click_through_rate = CASE
                WHEN engagement_metrics.impressions + 1 > 0
                THEN engagement_metrics.clicks::DECIMAL / (engagement_metrics.impressions + 1)
                ELSE 0
            END,
            engagement_rate = CASE
                WHEN engagement_metrics.impressions + 1 > 0
                THEN (engagement_metrics.reactions_count + engagement_metrics.comments_count + engagement_metrics.shares_count)::DECIMAL / (engagement_metrics.impressions + 1)
                ELSE 0
            END;
    END IF;

    -- Handle clicks
    IF TG_TABLE_NAME = 'post_clicks' AND TG_OP = 'INSERT' THEN
        INSERT INTO engagement_metrics (content_type, content_id, clicks, updated_at)
        VALUES (NEW.content_type, NEW.content_id, 1, NOW())
        ON CONFLICT (content_type, content_id)
        DO UPDATE SET
            clicks = engagement_metrics.clicks + 1,
            updated_at = NOW(),
            click_through_rate = CASE
                WHEN engagement_metrics.impressions > 0
                THEN (engagement_metrics.clicks + 1)::DECIMAL / engagement_metrics.impressions
                ELSE 0
            END;
    END IF;

    -- Handle shares
    IF TG_TABLE_NAME = 'share_tracking' AND TG_OP = 'INSERT' THEN
        INSERT INTO engagement_metrics (content_type, content_id, shares_count, updated_at)
        VALUES (NEW.content_type, NEW.content_id, 1, NOW())
        ON CONFLICT (content_type, content_id)
        DO UPDATE SET
            shares_count = engagement_metrics.shares_count + 1,
            last_interaction_at = NEW.created_at,
            updated_at = NOW(),
            engagement_rate = CASE
                WHEN engagement_metrics.impressions > 0
                THEN (engagement_metrics.reactions_count + engagement_metrics.comments_count + engagement_metrics.shares_count + 1)::DECIMAL / engagement_metrics.impressions
                ELSE 0
            END;
    END IF;

    -- Update trending score (weighted combination of CTR and engagement)
    UPDATE engagement_metrics
    SET trending_score = (
        (click_through_rate * 0.4) +
        (engagement_rate * 0.6)
    ) * (
        -- Time decay factor (newer content gets boost)
        CASE
            WHEN updated_at > NOW() - INTERVAL '24 hours' THEN 1.0
            WHEN updated_at > NOW() - INTERVAL '7 days' THEN 0.8
            WHEN updated_at > NOW() - INTERVAL '30 days' THEN 0.6
            ELSE 0.4
        END
    )
    WHERE content_type = COALESCE(NEW.content_type, OLD.content_type)
    AND content_id = COALESCE(NEW.content_id, OLD.content_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 9. Create triggers
CREATE TRIGGER update_engagement_on_impression
    AFTER INSERT ON post_impressions
    FOR EACH ROW EXECUTE FUNCTION update_engagement_metrics();

CREATE TRIGGER update_engagement_on_click
    AFTER INSERT ON post_clicks
    FOR EACH ROW EXECUTE FUNCTION update_engagement_metrics();

CREATE TRIGGER update_engagement_on_share
    AFTER INSERT ON share_tracking
    FOR EACH ROW EXECUTE FUNCTION update_engagement_metrics();

-- 10. Function to get trending posts for timeline algorithm
CREATE OR REPLACE FUNCTION get_trending_posts(
    user_id_param UUID DEFAULT NULL,
    limit_param INTEGER DEFAULT 20,
    offset_param INTEGER DEFAULT 0
)
RETURNS TABLE (
    content_type VARCHAR(20),
    content_id UUID,
    trending_score DECIMAL(8,4),
    click_through_rate DECIMAL(5,4),
    engagement_rate DECIMAL(5,4),
    impressions INTEGER,
    clicks INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        em.content_type,
        em.content_id,
        em.trending_score,
        em.click_through_rate,
        em.engagement_rate,
        em.impressions,
        em.clicks
    FROM engagement_metrics em
    WHERE em.trending_score > 0
    AND em.updated_at > NOW() - INTERVAL '30 days' -- Only recent content
    ORDER BY em.trending_score DESC, em.updated_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$ LANGUAGE plpgsql;
