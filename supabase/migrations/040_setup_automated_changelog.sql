-- Migration to automatically set up the OnlyDiary ChangeLog user and system
-- This runs automatically on deployment, no manual setup required

-- First, check if the ChangeLog user already exists
DO $$
DECLARE
    changelog_user_id UUID;
    changelog_exists BOOLEAN := FALSE;
BEGIN
    -- Check if ChangeLog user already exists
    SELECT id INTO changelog_user_id 
    FROM users 
    WHERE name = 'OnlyDiary ChangeLog' 
    LIMIT 1;
    
    IF changelog_user_id IS NOT NULL THEN
        changelog_exists := TRUE;
        RAISE NOTICE 'ChangeLog user already exists with ID: %', changelog_user_id;
    END IF;
    
    -- Only create if it doesn't exist
    IF NOT changelog_exists THEN
        -- Generate a new UUID for the ChangeLog user
        changelog_user_id := gen_random_uuid();
        
        -- Create the OnlyDiary ChangeLog user
        INSERT INTO users (
            id,
            email,
            name,
            role,
            bio,
            avatar,
            price_monthly,
            created_at,
            updated_at
        ) VALUES (
            changelog_user_id,
            '<EMAIL>',
            'OnlyDiary ChangeLog',
            'user',
            'The Code Book - Official OnlyDiary development updates and feature announcements. Subscribe to stay updated on new features, improvements, and behind-the-scenes development insights.',
            '📝',
            NULL, -- Free changelog (NULL means no subscription pricing)
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created ChangeLog user with ID: %', changelog_user_id;
        
        -- Create the welcome changelog entry
        INSERT INTO diary_entries (
            user_id,
            title,
            body_md,
            is_free,
            is_hidden,
            created_at,
            updated_at
        ) VALUES (
            changelog_user_id,
            'Welcome to The Code Book! 🎉',
            '# Welcome to The Code Book!

Hey everyone! 👋

This is The Code Book - OnlyDiary''s official development journal where I share daily updates about new features, improvements, and behind-the-scenes development insights.

## What to Expect

**Automated Updates**: Every time I push code changes, you''ll automatically see an update here. The system reads my git commit messages and translates them into user-friendly updates.

**Feature Announcements**: Be the first to know about new features as they''re deployed.

**Development Insights**: Get a peek behind the curtain at how OnlyDiary evolves daily.

**Community Feedback**: Your suggestions and feedback directly influence what gets built next.

## How It Works

- **Free Updates**: All changelog entries are free for everyone
- **Subscribe**: Hit the subscribe button to get these updates in your timeline
- **Timeline Integration**: Subscribed users will see changelog updates mixed in with their regular timeline
- **Automated**: No manual posting needed - updates happen with each deployment

## Privacy & Security

The system automatically filters out sensitive information like security fixes, internal system details, or anything that could expose the platform to risks.

## Let''s Build Together

OnlyDiary is built for you, and your feedback shapes every decision. Feel free to reach out with suggestions, bug reports, or just to say hi!

Ready to follow along on this journey? Hit that subscribe button! 🚀

---

*The Code Book is updated automatically with each deployment. Subscribe to never miss an update!*',
            true, -- Free entry
            false, -- Not hidden
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created welcome ChangeLog entry';
        
        RAISE NOTICE 'ChangeLog system setup completed successfully!';
    END IF;
END $$;

-- Create a function to easily get the ChangeLog user ID
CREATE OR REPLACE FUNCTION get_changelog_user_id()
RETURNS UUID AS $$
DECLARE
    user_id UUID;
BEGIN
    SELECT id INTO user_id 
    FROM users 
    WHERE name = 'OnlyDiary ChangeLog' 
    LIMIT 1;
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql;

-- Create a table to track processed commits to avoid duplicates
CREATE TABLE IF NOT EXISTS changelog_commits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    commit_hash TEXT UNIQUE NOT NULL,
    commit_message TEXT NOT NULL,
    entry_id UUID REFERENCES diary_entries(id),
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_changelog_commits_hash ON changelog_commits(commit_hash);
CREATE INDEX IF NOT EXISTS idx_changelog_commits_processed_at ON changelog_commits(processed_at);

-- Create a function to create changelog entries from git commits
CREATE OR REPLACE FUNCTION create_changelog_from_commit(
    commit_hash TEXT,
    commit_message TEXT,
    commit_author TEXT DEFAULT 'OnlyDiary Team',
    commit_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS UUID AS $$
DECLARE
    changelog_user_id UUID;
    new_entry_id UUID;
    entry_title TEXT;
    entry_content TEXT;
    existing_commit TEXT;
BEGIN
    -- Check if this commit was already processed
    SELECT commit_hash INTO existing_commit 
    FROM changelog_commits 
    WHERE commit_hash = create_changelog_from_commit.commit_hash;
    
    IF existing_commit IS NOT NULL THEN
        RAISE NOTICE 'Commit % already processed, skipping', commit_hash;
        RETURN NULL;
    END IF;
    
    -- Get the ChangeLog user ID
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    IF changelog_user_id IS NULL THEN
        RAISE EXCEPTION 'ChangeLog user not found. Please run the setup migration first.';
    END IF;
    
    -- Generate user-friendly title and content from commit message
    -- Extract first line as title, rest as description
    entry_title := SPLIT_PART(commit_message, E'\n', 1);
    
    -- If title is too technical or short, make it more user-friendly
    IF LENGTH(entry_title) < 10 OR entry_title ILIKE '%fix%' OR entry_title ILIKE '%update%' THEN
        entry_title := 'Platform Update - ' || TO_CHAR(commit_date, 'Month DD, YYYY');
    END IF;
    
    -- Create user-friendly content
    entry_content := '# ' || entry_title || E'\n\n';
    entry_content := entry_content || 'Hey everyone! 👋' || E'\n\n';
    entry_content := entry_content || 'Just pushed some updates to make OnlyDiary better:' || E'\n\n';
    entry_content := entry_content || '## What Changed' || E'\n';
    entry_content := entry_content || commit_message || E'\n\n';
    entry_content := entry_content || '## Technical Details' || E'\n';
    entry_content := entry_content || '- **Commit**: `' || LEFT(commit_hash, 8) || '`' || E'\n';
    entry_content := entry_content || '- **Author**: ' || commit_author || E'\n';
    entry_content := entry_content || '- **Date**: ' || TO_CHAR(commit_date, 'YYYY-MM-DD HH24:MI UTC') || E'\n\n';
    entry_content := entry_content || 'These improvements are live now! No action needed on your part.' || E'\n\n';
    entry_content := entry_content || '---' || E'\n';
    entry_content := entry_content || '*Have feedback or suggestions? Feel free to reach out!*';
    
    -- Create the new entry
    INSERT INTO diary_entries (
        user_id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        entry_title,
        entry_content,
        true, -- Always free
        false, -- Not hidden
        commit_date,
        NOW()
    ) RETURNING id INTO new_entry_id;
    
    -- Record that we processed this commit
    INSERT INTO changelog_commits (
        commit_hash,
        commit_message,
        entry_id,
        processed_at
    ) VALUES (
        create_changelog_from_commit.commit_hash,
        commit_message,
        new_entry_id,
        NOW()
    );
    
    RAISE NOTICE 'Created changelog entry % for commit %', new_entry_id, commit_hash;
    
    RETURN new_entry_id;
END;
$$ LANGUAGE plpgsql;

-- Create a view for easy ChangeLog management
CREATE OR REPLACE VIEW changelog_entries AS
SELECT 
    de.id,
    de.title,
    de.body_md,
    de.is_free,
    de.is_hidden,
    de.love_count,
    de.view_count,
    de.created_at,
    de.updated_at,
    u.name as author_name,
    u.avatar as author_avatar,
    cc.commit_hash,
    cc.commit_message
FROM diary_entries de
JOIN users u ON de.user_id = u.id
LEFT JOIN changelog_commits cc ON de.id = cc.entry_id
WHERE u.name = 'OnlyDiary ChangeLog'
ORDER BY de.created_at DESC;

-- Grant appropriate permissions
GRANT SELECT ON changelog_entries TO authenticated;
GRANT SELECT ON changelog_commits TO authenticated;
GRANT EXECUTE ON FUNCTION get_changelog_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION get_changelog_user_id() IS 'Returns the UUID of the OnlyDiary ChangeLog user';
COMMENT ON FUNCTION create_changelog_from_commit(TEXT, TEXT, TEXT, TIMESTAMP WITH TIME ZONE) IS 'Creates a changelog entry from git commit info. Prevents duplicates.';
COMMENT ON VIEW changelog_entries IS 'View of all ChangeLog entries with commit information';
COMMENT ON TABLE changelog_commits IS 'Tracks processed git commits to prevent duplicate changelog entries';

-- Final status message
DO $$
DECLARE
    changelog_user_id UUID;
    entry_count INTEGER;
BEGIN
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    SELECT COUNT(*) INTO entry_count
    FROM diary_entries 
    WHERE user_id = changelog_user_id;
    
    RAISE NOTICE '=== AUTOMATED CHANGELOG SETUP COMPLETE ===';
    RAISE NOTICE 'ChangeLog User ID: %', changelog_user_id;
    RAISE NOTICE 'Total Entries: %', entry_count;
    RAISE NOTICE 'ChangeLog URL: /changelog';
    RAISE NOTICE 'Automation: Ready for git commit processing';
    RAISE NOTICE '==========================================';
END $$;
