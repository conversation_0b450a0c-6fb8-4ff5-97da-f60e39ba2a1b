-- Fix function search_path security warnings ONLY
-- This addresses the remaining WARN level security issues

-- Fix get_reaction_counts function
CREATE OR REPLACE FUNCTION get_reaction_counts(entry_id uuid)
RETURNS TABLE(reaction_type text, count bigint) 
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.reaction_type::text,
    COUNT(*)::bigint
  FROM public.reactions r
  WHERE r.diary_entry_id = entry_id
  GROUP BY r.reaction_type;
END;
$$ LANGUAGE plpgsql;

-- Fix update_audio_love_count function
CREATE OR REPLACE FUNCTION update_audio_love_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.audio_post_id IS NOT NULL THEN
            UPDATE public.audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.audio_post_id;
        ELSIF NEW.audio_reply_id IS NOT NULL THEN
            UPDATE public.audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.audio_post_id IS NOT NULL THEN
            UPDATE public.audio_posts
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_post_id;
        ELSIF OLD.audio_reply_id IS NOT NULL THEN
            UPDATE public.audio_replies
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_audio_reply_count function
CREATE OR REPLACE FUNCTION update_audio_reply_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.audio_posts
        SET reply_count = reply_count + 1
        WHERE id = NEW.audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.audio_posts
        SET reply_count = GREATEST(reply_count - 1, 0)
        WHERE id = OLD.audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix assign_day1_badge function
CREATE OR REPLACE FUNCTION assign_day1_badge()
RETURNS TRIGGER 
SET search_path = ''
AS $$
DECLARE
    current_signup_count INTEGER;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM public.users WHERE id != NEW.id;
    
    -- Assign signup number (1-based)
    NEW.signup_number := current_signup_count + 1;
    
    -- Assign badge tier based on signup number
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'day1';
    ELSIF current_signup_count < 1000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'pioneer';
    ELSIF current_signup_count < 2500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'founder';
    ELSIF current_signup_count < 5000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'early_adopter';
    ELSIF current_signup_count < 10000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'charter_member';
    ELSE
        NEW.has_day1_badge := FALSE;
        NEW.badge_tier := NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix get_day1_badge_holders function (keep original return type)
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
)
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.created_at
    FROM public.users u
    WHERE u.has_day1_badge = TRUE
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix get_day1_badge_count function
CREATE OR REPLACE FUNCTION get_day1_badge_count()
RETURNS INTEGER 
SET search_path = ''
AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM public.users WHERE has_day1_badge = TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix update_book_audio_love_count function
CREATE OR REPLACE FUNCTION update_book_audio_love_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.book_audio_post_id IS NOT NULL THEN
            UPDATE public.book_audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_post_id;
        ELSIF NEW.book_audio_reply_id IS NOT NULL THEN
            UPDATE public.book_audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.book_audio_post_id IS NOT NULL THEN
            UPDATE public.book_audio_posts
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_post_id;
        ELSIF OLD.book_audio_reply_id IS NOT NULL THEN
            UPDATE public.book_audio_replies
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_book_audio_reply_count function
CREATE OR REPLACE FUNCTION update_book_audio_reply_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.book_audio_posts 
        SET reply_count = reply_count + 1 
        WHERE id = NEW.book_audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.book_audio_posts 
        SET reply_count = reply_count - 1 
        WHERE id = OLD.book_audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Fix update_withdrawals_updated_at function
CREATE OR REPLACE FUNCTION update_withdrawals_updated_at()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

SELECT 'Function search_path fixes applied successfully - Part 1' as status;
