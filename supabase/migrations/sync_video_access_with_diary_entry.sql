-- Sync video is_free status with diary entry is_free status
-- This ensures videos inherit the access level of their parent diary entry

-- <PERSON><PERSON> function to sync video access with diary entry
CREATE OR REPLACE FUNCTION sync_video_access_with_diary_entry()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update if is_free status actually changed
    IF OLD.is_free IS DISTINCT FROM NEW.is_free THEN
        -- Update all videos for this diary entry to match the new is_free status
        UPDATE videos 
        SET is_free = NEW.is_free,
            updated_at = NOW()
        WHERE post_id = NEW.id;
        
        -- Log the sync for debugging
        RAISE NOTICE 'Synced % videos for diary entry % to is_free=%', 
            (SELECT COUNT(*) FROM videos WHERE post_id = NEW.id),
            NEW.id, 
            NEW.is_free;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically sync video access when diary entry is updated
DROP TRIGGER IF EXISTS sync_video_access_trigger ON diary_entries;
CREATE TRIGGER sync_video_access_trigger
    AFTER UPDATE ON diary_entries
    FOR EACH ROW 
    EXECUTE FUNCTION sync_video_access_with_diary_entry();

-- One-time sync of existing videos to match their diary entry's is_free status
UPDATE videos 
SET is_free = de.is_free,
    updated_at = NOW()
FROM diary_entries de 
WHERE videos.post_id = de.id 
AND videos.is_free != de.is_free;

-- Add comment explaining the sync
COMMENT ON FUNCTION sync_video_access_with_diary_entry() IS 'Automatically syncs video is_free status with parent diary entry when diary entry is_free status changes';

-- Success message
SELECT 'Video access sync with diary entries configured successfully!' as status;
