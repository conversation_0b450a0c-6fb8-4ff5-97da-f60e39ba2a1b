-- Fix follower count trigger and function
-- This migration ensures that follower counts are automatically updated when users follow/unfollow

-- First, ensure the follower_count column exists in users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS follower_count INTEGER DEFAULT 0;

-- Fix the update_follower_count function with correct column names
CREATE OR REPLACE FUNCTION update_follower_count()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- When someone follows a user, increment their follower count
        UPDATE public.users 
        SET follower_count = follower_count + 1 
        WHERE id = NEW.writer_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- When someone unfollows a user, decrement their follower count
        UPDATE public.users 
        SET follower_count = GREATEST(follower_count - 1, 0) 
        WHERE id = OLD.writer_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists and create the correct one
DROP TRIGGER IF EXISTS update_follower_count_trigger ON follows;
CREATE TRIGGER update_follower_count_trigger
    AFTER INSERT OR DELETE ON follows
    FOR EACH ROW EXECUTE FUNCTION update_follower_count();

-- Initialize follower counts for existing users based on current follows
UPDATE users SET follower_count = (
    SELECT COUNT(*) 
    FROM follows 
    WHERE writer_id = users.id
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_follows_writer_id ON follows(writer_id);
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);

SELECT 'Follower count trigger fixed successfully!' as status;
