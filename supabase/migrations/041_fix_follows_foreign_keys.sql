-- Fix follows table foreign key constraints to reference public.users instead of auth.users
-- This fixes the "Failed to follow user" error when subscribing to free content like Code Book

-- Drop existing foreign key constraints
ALTER TABLE follows DROP CONSTRAINT IF EXISTS follows_follower_id_fkey;
ALTER TABLE follows DROP CONSTRAINT IF EXISTS follows_writer_id_fkey;

-- Add correct foreign key constraints that reference public.users
ALTER TABLE follows ADD CONSTRAINT follows_follower_id_fkey
FOREIGN KEY (follower_id) REFERENCES public.users(id) ON DELETE CASCADE;

ALTER TABLE follows ADD CONSTRAINT follows_writer_id_fkey
FOREIGN KEY (writer_id) REFERENCES public.users(id) ON DELETE CASCADE;

-- Ensure indexes exist for performance
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_writer_id ON follows(writer_id);
CREATE INDEX IF NOT EXISTS idx_follows_created_at ON follows(created_at);

-- Verify the fix worked
SELECT 'Follows table foreign keys fixed successfully!' as status;
