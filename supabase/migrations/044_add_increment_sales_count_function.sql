-- Create function to safely increment sales count
CREATE OR R<PERSON><PERSON>CE FUNCTION increment_sales_count(project_id UUID)
RETURNS void
SET search_path = ''
AS $$
BEGIN
    UPDATE public.projects 
    SET sales_count = COALESCE(sales_count, 0) + 1 
    WHERE id = project_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (for webhook calls)
GRANT EXECUTE ON FUNCTION increment_sales_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_sales_count(UUID) TO service_role;
