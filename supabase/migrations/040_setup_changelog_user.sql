-- Migration to automatically set up the OnlyDiary ChangeLog user and system
-- This runs automatically on deployment, no manual setup required

-- First, check if the ChangeLog user already exists
DO $$
DECLARE
    changelog_user_id UUID;
    changelog_exists BOOLEAN := FALSE;
BEGIN
    -- Check if ChangeLog user already exists
    SELECT id INTO changelog_user_id 
    FROM users 
    WHERE name = 'OnlyDiary ChangeLog' 
    LIMIT 1;
    
    IF changelog_user_id IS NOT NULL THEN
        changelog_exists := TRUE;
        RAISE NOTICE 'ChangeLog user already exists with ID: %', changelog_user_id;
    END IF;
    
    -- Only create if it doesn't exist
    IF NOT changelog_exists THEN
        -- Generate a new UUID for the ChangeLog user
        changelog_user_id := gen_random_uuid();
        
        -- Create the OnlyDiary ChangeLog user
        INSERT INTO users (
            id,
            email,
            name,
            role,
            bio,
            avatar,
            price_monthly,
            created_at,
            updated_at
        ) VALUES (
            changelog_user_id,
            '<EMAIL>',
            'OnlyDiary ChangeLog',
            'user',
            'Official OnlyDiary development updates and feature announcements. Subscribe to stay updated on new features, improvements, and behind-the-scenes development insights.',
            '📝',
            0, -- Free changelog
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created ChangeLog user with ID: %', changelog_user_id;
        
        -- Create the welcome changelog entry
        INSERT INTO diary_entries (
            user_id,
            title,
            body_md,
            is_free,
            is_hidden,
            created_at,
            updated_at
        ) VALUES (
            changelog_user_id,
            'Welcome to the OnlyDiary ChangeLog! 🎉',
            '# Welcome to the OnlyDiary ChangeLog!

Hey everyone! 👋

This is the official OnlyDiary ChangeLog where I''ll be sharing daily updates about new features, improvements, and behind-the-scenes development insights.

## What to Expect

**Daily Updates**: Every time I push a new version or make significant improvements, you''ll see an update here. I''ll write in plain language with just a touch of technical detail - enough to understand what''s happening without getting lost in the weeds.

**Feature Announcements**: Be the first to know about new features before they''re announced anywhere else.

**Development Insights**: Get a peek behind the curtain at how OnlyDiary is built and evolved.

**Community Feedback**: Your suggestions and feedback directly influence what gets built next.

## How It Works

- **Free Updates**: Most changelog entries will be free for everyone
- **Subscribe**: Hit the subscribe button to get these updates in your timeline
- **Timeline Integration**: Subscribed users will see changelog updates mixed in with their regular timeline

## Privacy & Security

Don''t worry - I won''t be sharing anything that could expose the platform to security risks or give competitors an unfair advantage. This is about transparency and keeping you informed, not about revealing trade secrets.

## Let''s Build Together

OnlyDiary is built for you, and your feedback shapes every decision. Feel free to reach out with suggestions, bug reports, or just to say hi!

Ready to follow along on this journey? Hit that subscribe button! 🚀

---

*This changelog is updated daily with each deployment. Subscribe to never miss an update!*',
            true, -- Free entry
            false, -- Not hidden
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created welcome ChangeLog entry';
        
        -- Update user counts if the trigger doesn't handle it
        UPDATE users 
        SET entry_count = 1 
        WHERE id = changelog_user_id;
        
        RAISE NOTICE 'ChangeLog system setup completed successfully!';
    END IF;
END $$;

-- Create a function to easily get the ChangeLog user ID for future use
CREATE OR REPLACE FUNCTION get_changelog_user_id()
RETURNS UUID AS $$
DECLARE
    user_id UUID;
BEGIN
    SELECT id INTO user_id 
    FROM users 
    WHERE name = 'OnlyDiary ChangeLog' 
    LIMIT 1;
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql;

-- Create a function to create new changelog entries programmatically
CREATE OR REPLACE FUNCTION create_changelog_entry(
    entry_title TEXT,
    entry_content TEXT,
    is_free_entry BOOLEAN DEFAULT true
)
RETURNS UUID AS $$
DECLARE
    changelog_user_id UUID;
    new_entry_id UUID;
BEGIN
    -- Get the ChangeLog user ID
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    IF changelog_user_id IS NULL THEN
        RAISE EXCEPTION 'ChangeLog user not found. Please run the setup migration first.';
    END IF;
    
    -- Create the new entry
    INSERT INTO diary_entries (
        user_id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        entry_title,
        entry_content,
        is_free_entry,
        false, -- Not hidden
        NOW(),
        NOW()
    ) RETURNING id INTO new_entry_id;
    
    RETURN new_entry_id;
END;
$$ LANGUAGE plpgsql;

-- Create a view for easy ChangeLog management
CREATE OR REPLACE VIEW changelog_entries AS
SELECT 
    de.id,
    de.title,
    de.body_md,
    de.is_free,
    de.is_hidden,
    de.love_count,
    de.view_count,
    de.created_at,
    de.updated_at,
    u.name as author_name,
    u.avatar as author_avatar
FROM diary_entries de
JOIN users u ON de.user_id = u.id
WHERE u.name = 'OnlyDiary ChangeLog'
ORDER BY de.created_at DESC;

-- Grant appropriate permissions
GRANT SELECT ON changelog_entries TO authenticated;
GRANT EXECUTE ON FUNCTION get_changelog_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION create_changelog_entry(TEXT, TEXT, BOOLEAN) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION get_changelog_user_id() IS 'Returns the UUID of the OnlyDiary ChangeLog user';
COMMENT ON FUNCTION create_changelog_entry(TEXT, TEXT, BOOLEAN) IS 'Creates a new changelog entry. Parameters: title, content, is_free (default true)';
COMMENT ON VIEW changelog_entries IS 'View of all ChangeLog entries with author information';

-- Final status message
DO $$
DECLARE
    changelog_user_id UUID;
    entry_count INTEGER;
BEGIN
    SELECT get_changelog_user_id() INTO changelog_user_id;
    
    SELECT COUNT(*) INTO entry_count
    FROM diary_entries 
    WHERE user_id = changelog_user_id;
    
    RAISE NOTICE '=== CHANGELOG SETUP COMPLETE ===';
    RAISE NOTICE 'ChangeLog User ID: %', changelog_user_id;
    RAISE NOTICE 'Total Entries: %', entry_count;
    RAISE NOTICE 'ChangeLog URL: /changelog';
    RAISE NOTICE 'Admin Management: /admin (ChangeLog tab)';
    RAISE NOTICE '================================';
END $$;
