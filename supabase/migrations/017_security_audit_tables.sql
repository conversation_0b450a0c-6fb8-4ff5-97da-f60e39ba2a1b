-- Security audit and monitoring tables
-- Run this in Supabase SQL editor

-- File access audit log
CREATE TABLE IF NOT EXISTS public.file_access_logs (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  file_key text NOT NULL,
  file_type text, -- 'video', 'audio', 'image', 'document'
  action text NOT NULL, -- 'upload', 'download', 'delete', 'view'
  ip_address inet,
  user_agent text,
  endpoint text,
  success boolean DEFAULT true,
  error_message text,
  file_size_bytes bigint,
  metadata jsonb,
  created_at timestamptz DEFAULT NOW()
);

-- Security events audit log
CREATE TABLE IF NOT EXISTS public.security_events (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type text NOT NULL,
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  user_email text,
  ip_address inet,
  user_agent text,
  endpoint text,
  severity text NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
  details jsonb,
  created_at timestamptz DEFAULT NOW()
);

-- Rate limit violations log
CREATE TABLE IF NOT EXISTS public.rate_limit_violations (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  ip_address inet NOT NULL,
  endpoint text NOT NULL,
  limit_type text NOT NULL,
  violation_count integer DEFAULT 1,
  first_violation_at timestamptz DEFAULT NOW(),
  last_violation_at timestamptz DEFAULT NOW(),
  user_id uuid REFERENCES public.users(id) ON DELETE SET NULL,
  user_agent text,
  created_at timestamptz DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_file_access_logs_user_id ON public.file_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_file_access_logs_created_at ON public.file_access_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_file_access_logs_file_key ON public.file_access_logs(file_key);
CREATE INDEX IF NOT EXISTS idx_file_access_logs_action ON public.file_access_logs(action);

CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON public.security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON public.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON public.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON public.security_events(ip_address);

CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_ip_address ON public.rate_limit_violations(ip_address);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_endpoint ON public.rate_limit_violations(endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_created_at ON public.rate_limit_violations(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE public.file_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rate_limit_violations ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Only admins can view audit logs
CREATE POLICY "Admins can view file access logs" ON public.file_access_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can view security events" ON public.security_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can view rate limit violations" ON public.rate_limit_violations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Users can view their own file access logs
CREATE POLICY "Users can view own file access logs" ON public.file_access_logs
  FOR SELECT USING (user_id = auth.uid());

-- Grant permissions
GRANT SELECT ON public.file_access_logs TO authenticated;
GRANT SELECT ON public.security_events TO authenticated;
GRANT SELECT ON public.rate_limit_violations TO authenticated;

-- Functions for audit logging
CREATE OR REPLACE FUNCTION log_file_access(
  p_user_id uuid,
  p_file_key text,
  p_file_type text,
  p_action text,
  p_ip_address text DEFAULT NULL,
  p_user_agent text DEFAULT NULL,
  p_endpoint text DEFAULT NULL,
  p_success boolean DEFAULT true,
  p_error_message text DEFAULT NULL,
  p_file_size_bytes bigint DEFAULT NULL,
  p_metadata jsonb DEFAULT NULL
)
RETURNS uuid
SET search_path = ''
AS $$
DECLARE
  log_id uuid;
BEGIN
  INSERT INTO public.file_access_logs (
    user_id,
    file_key,
    file_type,
    action,
    ip_address,
    user_agent,
    endpoint,
    success,
    error_message,
    file_size_bytes,
    metadata
  ) VALUES (
    p_user_id,
    p_file_key,
    p_file_type,
    p_action,
    p_ip_address::inet,
    p_user_agent,
    p_endpoint,
    p_success,
    p_error_message,
    p_file_size_bytes,
    p_metadata
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION log_security_event(
  p_event_type text,
  p_user_id uuid DEFAULT NULL,
  p_user_email text DEFAULT NULL,
  p_ip_address text DEFAULT NULL,
  p_user_agent text DEFAULT NULL,
  p_endpoint text DEFAULT NULL,
  p_severity text DEFAULT 'MEDIUM',
  p_details jsonb DEFAULT NULL
)
RETURNS uuid
SET search_path = ''
AS $$
DECLARE
  event_id uuid;
BEGIN
  INSERT INTO public.security_events (
    event_type,
    user_id,
    user_email,
    ip_address,
    user_agent,
    endpoint,
    severity,
    details
  ) VALUES (
    p_event_type,
    p_user_id,
    p_user_email,
    p_ip_address::inet,
    p_user_agent,
    p_endpoint,
    p_severity,
    p_details
  ) RETURNING id INTO event_id;
  
  RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get security statistics
CREATE OR REPLACE FUNCTION get_security_stats(days_back integer DEFAULT 7)
RETURNS TABLE(
  total_file_accesses bigint,
  failed_file_accesses bigint,
  security_events_by_severity jsonb,
  top_violated_endpoints jsonb,
  unique_ips_with_violations bigint
)
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM public.file_access_logs 
     WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL) as total_file_accesses,
    
    (SELECT COUNT(*) FROM public.file_access_logs 
     WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL 
     AND success = false) as failed_file_accesses,
    
    (SELECT jsonb_object_agg(severity, count)
     FROM (
       SELECT severity, COUNT(*) as count
       FROM public.security_events 
       WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL
       GROUP BY severity
     ) t) as security_events_by_severity,
    
    (SELECT jsonb_agg(jsonb_build_object('endpoint', endpoint, 'violations', violation_count))
     FROM (
       SELECT endpoint, COUNT(*) as violation_count
       FROM public.rate_limit_violations 
       WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL
       GROUP BY endpoint
       ORDER BY violation_count DESC
       LIMIT 10
     ) t) as top_violated_endpoints,
    
    (SELECT COUNT(DISTINCT ip_address) 
     FROM public.rate_limit_violations 
     WHERE created_at >= NOW() - (days_back || ' days')::INTERVAL) as unique_ips_with_violations;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION log_file_access TO authenticated;
GRANT EXECUTE ON FUNCTION log_security_event TO authenticated;
GRANT EXECUTE ON FUNCTION get_security_stats TO authenticated;
