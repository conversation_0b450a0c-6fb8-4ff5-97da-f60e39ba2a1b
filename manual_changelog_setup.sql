-- Manual ChangeLog User Setup
-- Run this if the automated migration didn't work

-- First, check if the ChangeLog user exists
SELECT id, name, email, bio 
FROM users 
WHERE name = 'OnlyDiary ChangeLog' OR email = '<EMAIL>';

-- If no results above, run this to create the user manually:
DO $$
DECLARE
    changelog_user_id UUID;
BEGIN
    -- Generate a new UUID for the ChangeLog user
    changelog_user_id := gen_random_uuid();
    
    -- Create the OnlyDiary ChangeLog user
    INSERT INTO users (
        id,
        email,
        name,
        role,
        bio,
        avatar,
        price_monthly,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        '<EMAIL>',
        'OnlyDiary ChangeLog',
        'user',
        'The Code Book - Official OnlyDiary development updates and feature announcements. Subscribe to stay updated on new features, improvements, and behind-the-scenes development insights.',
        '📝',
        NULL, -- Free changelog (NULL means no subscription pricing)
        NOW(),
        NOW()
    );
    
    RAISE NOTICE 'Created ChangeLog user with ID: %', changelog_user_id;
    
    -- Create the welcome changelog entry
    INSERT INTO diary_entries (
        user_id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at
    ) VALUES (
        changelog_user_id,
        'Welcome to The Code Book! 🎉',
        '# Welcome to The Code Book!

Hey everyone! 👋

This is The Code Book - OnlyDiary''s official development journal where I share daily updates about new features, improvements, and behind-the-scenes development insights.

## What to Expect

**Automated Updates**: Every time I push code changes, you''ll automatically see an update here. The system reads my git commit messages and translates them into user-friendly updates.

**Feature Announcements**: Be the first to know about new features as they''re deployed.

**Development Insights**: Get a peek behind the curtain at how OnlyDiary evolves daily.

**Community Feedback**: Your suggestions and feedback directly influence what gets built next.

## How It Works

- **Free Updates**: All changelog entries are free for everyone
- **Subscribe**: Hit the subscribe button to get these updates in your timeline
- **Timeline Integration**: Subscribed users will see changelog updates mixed in with their regular timeline
- **Automated**: No manual posting needed - updates happen with each deployment

## Privacy & Security

The system automatically filters out sensitive information like security fixes, internal system details, or anything that could expose the platform to risks.

## Let''s Build Together

OnlyDiary is built for you, and your feedback shapes every decision. Feel free to reach out with suggestions, bug reports, or just to say hi!

Ready to follow along on this journey? Hit that subscribe button! 🚀

---

*The Code Book is updated automatically with each deployment. Subscribe to never miss an update!*',
        true, -- Free entry
        false, -- Not hidden
        NOW(),
        NOW()
    );
    
    RAISE NOTICE 'Created welcome ChangeLog entry';
    RAISE NOTICE 'ChangeLog system setup completed successfully!';
END $$;

-- Verify the setup worked
SELECT 
    u.id,
    u.name,
    u.email,
    u.bio,
    COUNT(de.id) as entry_count
FROM users u
LEFT JOIN diary_entries de ON u.id = de.user_id
WHERE u.name = 'OnlyDiary ChangeLog'
GROUP BY u.id, u.name, u.email, u.bio;
