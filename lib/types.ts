export interface Creator {
  id: string;
  name: string | null;
  email: string;
  role: string | null;
  bio?: string;
  avatar?: string;
  profile_picture_url?: string;
  follower_count?: number;
  subscriber_count?: number;
  entry_count?: number;
  is_following?: boolean;
  stripe_account_id: string | null;
  stripe_onboarding_complete: boolean | null;
  price_monthly: number | null;
  created_at: string | null;
  updated_at: string | null;
  custom_url: string | null;
  hide_subscriber_count?: boolean;
}

export interface SubscriptionStat {
  writer_id: string;
  status: string;
  created_at: string | null;
}

export interface PaymentActivity {
  id: string;
  writer_id: string | null;
  amount_cents: number;
  created_at: string | null;
  users: { name: string | null } | null;
}
