import { TutorialStep } from '@/contexts/TutorialContext'

export const tutorialSteps: TutorialStep[] = [
  // Welcome Category - Platform Introduction
  {
    id: 'welcome-1',
    title: 'Welcome to OnlyDiary! 👋',
    content: 'OnlyDiary is a new kind of social network built on authentic diary entries instead of posts. Here, you share your real thoughts and experiences, and people connect with the authentic you.',
    position: 'center',
    category: 'welcome',
    order: 1
  },
  {
    id: 'welcome-2',
    title: 'Your Dashboard',
    content: 'This is your creative hub where you can manage your diary entries, track your progress, and see how your stories are performing. Think of it as your personal writing studio.',
    target: '[data-tutorial="dashboard-header"]',
    position: 'bottom',
    page: '/dashboard',
    category: 'welcome',
    order: 2
  },
  {
    id: 'welcome-3',
    title: 'Navigation Made Simple',
    content: 'Use the navigation to explore: Dashboard for managing content, Timeline for discovering stories, Create for sharing thoughts, and your Profile to customize your presence.',
    target: 'nav',
    position: 'bottom',
    category: 'welcome',
    order: 3
  },
  {
    id: 'welcome-4',
    title: 'Ready to Start?',
    content: 'Let\'s explore the key features that make OnlyDiary special. You can always return to this tutorial by clicking the help button.',
    position: 'center',
    category: 'welcome',
    order: 4
  },

  // Writing Category - Creating Content
  {
    id: 'writing-1',
    title: 'Share Your Story 📝',
    content: 'Click "Create" to start writing your first diary entry. This is where your authentic voice shines through.',
    target: '[data-tutorial="create-button"]',
    position: 'bottom',
    action: 'click',
    category: 'writing',
    order: 1
  },
  {
    id: 'writing-2',
    title: 'Craft Your Entry',
    content: 'Give your entry a meaningful title and write from the heart. Share what\'s really on your mind - your readers want to know the real you.',
    target: '[data-tutorial="entry-form"]',
    position: 'right',
    page: '/write/diary',
    category: 'writing',
    order: 2
  },
  {
    id: 'writing-3',
    title: 'Add Visual Stories',
    content: 'Bring your diary to life with photos or videos. You can add one type of media per entry to complement your words.',
    target: '[data-tutorial="media-upload"]',
    position: 'top',
    page: '/write/diary',
    category: 'writing',
    order: 3
  },
  {
    id: 'writing-4',
    title: 'Choose Your Audience',
    content: 'Decide how to share: Free for everyone, with subscribers only, or accept donations. You control who sees your content.',
    target: '[data-tutorial="monetization-options"]',
    position: 'left',
    page: '/write/diary',
    category: 'writing',
    order: 4
  },
  {
    id: 'writing-5',
    title: 'Publish & Connect',
    content: 'Hit "Save" when you\'re ready to share with the world. Your authentic story will find its audience.',
    target: '[data-tutorial="save-button"]',
    position: 'top',
    page: '/write/diary',
    category: 'writing',
    order: 5
  },

  // Audio Category - Voice Features
  {
    id: 'audio-1',
    title: 'Your Voice Matters 🎤',
    content: 'OnlyDiary features unique 9-second audio posts called OnlyAudio. Share quick thoughts, reactions, or moments with your voice to create more intimate connections.',
    position: 'center',
    category: 'audio',
    order: 1
  },
  {
    id: 'audio-2',
    title: 'Find Audio Features',
    content: 'Look for the microphone icon in your dashboard or timeline to access audio posting features. Audio posts appear alongside diary entries.',
    target: '[data-tutorial="audio-section"]',
    position: 'bottom',
    page: '/dashboard',
    category: 'audio',
    order: 2
  },
  {
    id: 'audio-3',
    title: 'Record Audio Posts',
    content: 'Create intimate 9-second audio moments that let your personality shine through. Perfect for sharing quick thoughts, reactions, or behind-the-scenes moments.',
    target: '[data-tutorial="audio-recorder"]',
    position: 'bottom',
    category: 'audio',
    order: 3
  },
  {
    id: 'audio-4',
    title: 'Audio Conversations',
    content: 'Reply to audio posts with your own voice recordings, creating threaded conversations that feel more personal than text. Build deeper connections through voice.',
    target: '[data-tutorial="audio-replies"]',
    position: 'right',
    category: 'audio',
    order: 4
  },
  {
    id: 'audio-5',
    title: 'Audio in Timeline',
    content: 'Your audio posts appear in the timeline alongside diary entries and books, giving followers a complete picture of your authentic voice.',
    target: '[data-tutorial="timeline-audio"]',
    position: 'left',
    page: '/timeline',
    category: 'audio',
    order: 5
  },

  // Profile Category - Personal Setup
  {
    id: 'profile-1',
    title: 'Your Digital Identity 👤',
    content: 'Your profile is how readers discover and connect with you. Let\'s make it represent the real you.',
    target: '[data-tutorial="profile-section"]',
    position: 'bottom',
    page: '/profile/edit',
    category: 'profile',
    order: 1
  },
  {
    id: 'profile-2',
    title: 'Set Your Rates',
    content: 'Choose your monthly subscription price or offer free content with donations. You control your monetization strategy.',
    target: '[data-tutorial="pricing-section"]',
    position: 'left',
    page: '/profile/edit',
    category: 'profile',
    order: 2
  },
  {
    id: 'profile-3',
    title: 'Custom Profile URL',
    content: 'Create a memorable URL like onlydiary.app/yourname to make it easy for people to find and share your profile.',
    target: '[data-tutorial="custom-url"]',
    position: 'right',
    page: '/profile/edit',
    category: 'profile',
    order: 3
  },

  // Discovery Category - Finding Content
  {
    id: 'discovery-1',
    title: 'Discover Stories 🌟',
    content: 'The Timeline shows you the latest stories from creators you follow and discover. Find authentic voices and genuine connections based on real experiences.',
    target: '[data-tutorial="timeline-header"]',
    position: 'bottom',
    page: '/timeline',
    category: 'discovery',
    order: 1
  },
  {
    id: 'discovery-2',
    title: 'Follow vs Subscribe',
    content: 'Follow creators for free to see their public content, or subscribe monthly to access their premium diary entries and directly support their creative work.',
    target: '[data-tutorial="follow-subscribe"]',
    position: 'top',
    category: 'discovery',
    order: 2
  },
  {
    id: 'discovery-3',
    title: 'Engage Authentically',
    content: 'React with hearts, leave thoughtful comments, and share stories that resonate with you. OnlyDiary encourages meaningful interactions over quick likes.',
    target: '[data-tutorial="engagement-buttons"]',
    position: 'bottom',
    category: 'discovery',
    order: 3
  },
  {
    id: 'discovery-4',
    title: 'Explore Profiles',
    content: 'Visit creator profiles to see their complete story - diary entries, audio posts, and books all in one place. Get to know the whole person.',
    target: '[data-tutorial="profile-link"]',
    position: 'right',
    category: 'discovery',
    order: 4
  },
  {
    id: 'discovery-5',
    title: 'Support Creators',
    content: 'Use the donation feature to support creators whose stories move you, even if you\'re not ready for a full subscription.',
    target: '[data-tutorial="donation-button"]',
    position: 'top',
    category: 'discovery',
    order: 5
  },

  // Advanced Category - Books & Features
  {
    id: 'advanced-1',
    title: 'Create Books 📚',
    content: 'Turn your diary entries into published books, or upload complete manuscripts. OnlyDiary supports both chapter-by-chapter creation and full book uploads.',
    target: '[data-tutorial="books-section"]',
    position: 'bottom',
    page: '/books',
    category: 'advanced',
    order: 1
  },
  {
    id: 'advanced-2',
    title: 'Book Creation Options',
    content: 'Choose between manual chapter creation for ongoing projects or upload complete formatted books. Both integrate seamlessly with your profile.',
    target: '[data-tutorial="book-creation"]',
    position: 'right',
    page: '/write/upload-ebook',
    category: 'advanced',
    order: 2
  },
  {
    id: 'advanced-3',
    title: 'Track Your Growth',
    content: 'Monitor your earnings, subscriber growth, and content performance in your dashboard. Understand what resonates with your audience.',
    target: '[data-tutorial="analytics-section"]',
    position: 'left',
    page: '/dashboard',
    category: 'advanced',
    order: 3
  },
  {
    id: 'advanced-4',
    title: 'Build Your Community',
    content: 'Use the mailing list feature to stay connected with your most engaged readers and announce new content or book releases.',
    target: '[data-tutorial="mailing-list"]',
    position: 'right',
    page: '/dashboard',
    category: 'advanced',
    order: 4
  },
  {
    id: 'advanced-5',
    title: 'Monetization Mastery',
    content: 'Combine subscriptions, book sales, and donations to create multiple revenue streams. OnlyDiary gives you full control over your creative business.',
    target: '[data-tutorial="monetization-overview"]',
    position: 'center',
    page: '/dashboard',
    category: 'advanced',
    order: 5
  }
]

// Helper functions for tutorial step management
export function getStepsByCategory(category: string): TutorialStep[] {
  return tutorialSteps
    .filter(step => step.category === category)
    .sort((a, b) => a.order - b.order)
}

export function getStepById(stepId: string): TutorialStep | undefined {
  return tutorialSteps.find(step => step.id === stepId)
}

export function getNextStep(currentStepId: string): TutorialStep | null {
  const currentStep = getStepById(currentStepId)
  if (!currentStep) return null

  const categorySteps = getStepsByCategory(currentStep.category)
  const currentIndex = categorySteps.findIndex(step => step.id === currentStepId)
  
  if (currentIndex === -1 || currentIndex === categorySteps.length - 1) {
    return null
  }
  
  return categorySteps[currentIndex + 1]
}

export function getPreviousStep(currentStepId: string): TutorialStep | null {
  const currentStep = getStepById(currentStepId)
  if (!currentStep) return null

  const categorySteps = getStepsByCategory(currentStep.category)
  const currentIndex = categorySteps.findIndex(step => step.id === currentStepId)
  
  if (currentIndex <= 0) {
    return null
  }
  
  return categorySteps[currentIndex - 1]
}

export function getCategoryProgress(category: string, completedSteps: string[]): {
  completed: number
  total: number
  percentage: number
} {
  const categorySteps = getStepsByCategory(category)
  const completed = categorySteps.filter(step => completedSteps.includes(step.id)).length
  
  return {
    completed,
    total: categorySteps.length,
    percentage: categorySteps.length > 0 ? Math.round((completed / categorySteps.length) * 100) : 0
  }
}

export const tutorialCategories = [
  {
    id: 'welcome',
    name: 'Welcome Tour',
    description: 'Get started with OnlyDiary basics',
    icon: '👋',
    color: 'blue'
  },
  {
    id: 'writing',
    name: 'Writing Guide',
    description: 'Learn to create compelling diary entries',
    icon: '📝',
    color: 'green'
  },
  {
    id: 'audio',
    name: 'Audio Features',
    description: 'Discover voice-based content creation',
    icon: '🎤',
    color: 'purple'
  },
  {
    id: 'profile',
    name: 'Profile Setup',
    description: 'Customize your presence and monetization',
    icon: '👤',
    color: 'orange'
  },
  {
    id: 'discovery',
    name: 'Discovery & Engagement',
    description: 'Find and connect with other creators',
    icon: '🌟',
    color: 'pink'
  },
  {
    id: 'advanced',
    name: 'Advanced Features',
    description: 'Books, analytics, and community building',
    icon: '📚',
    color: 'indigo'
  }
] as const
