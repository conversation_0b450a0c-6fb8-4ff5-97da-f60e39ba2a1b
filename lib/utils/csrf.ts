/**
 * CSRF (Cross-Site Request Forgery) Protection
 * Prevents malicious sites from making unauthorized requests on behalf of users
 */

import { NextRequest } from 'next/server'
import { createHash, randomBytes } from 'crypto'

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32
const CSRF_TOKEN_HEADER = 'x-csrf-token'
const CSRF_TOKEN_COOKIE = 'csrf-token'
const CSRF_SECRET_HEADER = 'x-csrf-secret'

/**
 * Generates a cryptographically secure CSRF token
 * @returns Object with token and secret
 */
export function generateCSRFToken(): { token: string; secret: string } {
  const secret = randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
  const token = randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
  
  return { token, secret }
}

/**
 * Creates a hash of the token and secret for verification
 * @param token - CSRF token
 * @param secret - CSRF secret
 * @returns Hashed value for verification
 */
function createCSRFHash(token: string, secret: string): string {
  return createHash('sha256')
    .update(`${token}:${secret}`)
    .digest('hex')
}

/**
 * Validates CSRF token against the secret
 * @param token - CSRF token from request
 * @param secret - CSRF secret from session/cookie
 * @returns boolean - True if valid
 */
export function validateCSRFToken(token: string, secret: string): boolean {
  if (!token || !secret) {
    return false
  }
  
  try {
    // Create expected hash
    const expectedHash = createCSRFHash(token, secret)
    const providedHash = createCSRFHash(token, secret)
    
    // Use timing-safe comparison
    return timingSafeEqual(expectedHash, providedHash)
  } catch (error) {
    console.error('CSRF validation error:', error)
    return false
  }
}

/**
 * Timing-safe string comparison to prevent timing attacks
 * @param a - First string
 * @param b - Second string
 * @returns boolean - True if strings are equal
 */
function timingSafeEqual(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false
  }
  
  let result = 0
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i)
  }
  
  return result === 0
}

/**
 * Extracts CSRF token from request headers or body
 * @param request - NextRequest object
 * @returns CSRF token or null
 */
export function getCSRFTokenFromRequest(request: NextRequest): string | null {
  // Check header first
  const headerToken = request.headers.get(CSRF_TOKEN_HEADER)
  if (headerToken) {
    return headerToken
  }
  
  // For form submissions, token might be in body
  // This would need to be handled in the API route after parsing body
  return null
}

/**
 * Extracts CSRF secret from request cookies or headers
 * @param request - NextRequest object
 * @returns CSRF secret or null
 */
export function getCSRFSecretFromRequest(request: NextRequest): string | null {
  // Check custom header first (for API calls)
  const headerSecret = request.headers.get(CSRF_SECRET_HEADER)
  if (headerSecret) {
    return headerSecret
  }
  
  // Check cookie (for form submissions)
  const cookieSecret = request.cookies.get(CSRF_TOKEN_COOKIE)?.value
  if (cookieSecret) {
    return cookieSecret
  }
  
  return null
}

/**
 * Validates CSRF protection for a request
 * @param request - NextRequest object
 * @returns Object with validation result and error message
 */
export function validateCSRFProtection(request: NextRequest): {
  isValid: boolean
  error?: string
} {
  // Skip CSRF for GET, HEAD, OPTIONS requests (safe methods)
  const method = request.method.toUpperCase()
  if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
    return { isValid: true }
  }
  
  // Skip CSRF for same-origin requests (additional check)
  const origin = request.headers.get('origin')
  const host = request.headers.get('host')
  
  if (origin && host) {
    try {
      const originUrl = new URL(origin)
      const isLocalhost = host.includes('localhost') || host.includes('127.0.0.1')
      const isSameOrigin = originUrl.host === host
      
      // Allow localhost in development
      if (process.env.NODE_ENV === 'development' && isLocalhost) {
        return { isValid: true }
      }
      
      // Allow same-origin requests
      if (isSameOrigin) {
        return { isValid: true }
      }
    } catch (error) {
      // Invalid origin URL
      return { isValid: false, error: 'Invalid origin header' }
    }
  }
  
  // For cross-origin requests, require CSRF token
  const token = getCSRFTokenFromRequest(request)
  const secret = getCSRFSecretFromRequest(request)
  
  if (!token) {
    return { isValid: false, error: 'CSRF token required' }
  }
  
  if (!secret) {
    return { isValid: false, error: 'CSRF secret required' }
  }
  
  const isValid = validateCSRFToken(token, secret)
  
  return {
    isValid,
    error: isValid ? undefined : 'Invalid CSRF token'
  }
}

/**
 * Creates CSRF protection middleware for API routes
 * @param handler - The API route handler
 * @returns Protected API route handler
 */
export function withCSRFProtection(
  handler: (request: NextRequest, context: any) => Promise<Response>
) {
  return async (request: NextRequest, context: any): Promise<Response> => {
    // Validate CSRF protection
    const csrfResult = validateCSRFProtection(request)
    
    if (!csrfResult.isValid) {
      return new Response(
        JSON.stringify({ 
          error: 'CSRF validation failed',
          message: csrfResult.error 
        }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
    
    // CSRF validation passed, proceed with original handler
    return handler(request, context)
  }
}

/**
 * Creates response headers for CSRF token setup
 * @param token - CSRF token
 * @param secret - CSRF secret
 * @returns Headers object
 */
export function createCSRFHeaders(token: string, secret: string): Record<string, string> {
  return {
    'X-CSRF-Token': token,
    'Set-Cookie': `${CSRF_TOKEN_COOKIE}=${secret}; HttpOnly; Secure; SameSite=Strict; Path=/`
  }
}

/**
 * Checks if request needs CSRF protection
 * @param request - NextRequest object
 * @returns boolean - True if CSRF protection is needed
 */
export function needsCSRFProtection(request: NextRequest): boolean {
  const method = request.method.toUpperCase()
  
  // Only protect state-changing methods
  return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)
}

/**
 * Creates a CSRF error response
 * @param message - Error message
 * @returns Response object
 */
export function createCSRFError(message: string = 'CSRF validation failed'): Response {
  return new Response(
    JSON.stringify({ error: message }),
    { 
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}
