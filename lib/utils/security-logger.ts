/**
 * Security Event Logging Utility
 * Tracks security-related events for monitoring and auditing
 */

import { NextRequest } from 'next/server'

// Security event types
export type SecurityEventType = 
  | 'AUTH_SUCCESS'
  | 'AUTH_FAILURE' 
  | 'ADMIN_LOGIN'
  | 'ADMIN_LOGIN_FAILED'
  | 'FILE_UPLOAD'
  | 'FILE_ACCESS'
  | 'RATE_LIMIT_HIT'
  | 'CSRF_VIOLATION'
  | 'INVALID_UUID'
  | 'CRON_ACCESS'
  | 'SUSPICIOUS_ACTIVITY'
  | 'PASSWORD_CHANGE'
  | 'ACCOUNT_CREATION'

// Security event data structure
interface SecurityEvent {
  type: SecurityEventType
  userId?: string
  userEmail?: string
  ipAddress?: string
  userAgent?: string
  endpoint?: string
  details?: Record<string, any>
  timestamp: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

/**
 * Gets client IP address from request headers
 */
function getClientIP(request: NextRequest): string {
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  return forwardedFor?.split(',')[0]?.trim() || 
         realIp || 
         cfConnectingIp || 
         request.ip || 
         'unknown'
}

/**
 * Gets user agent from request headers
 */
function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown'
}

/**
 * Determines severity level based on event type
 */
function getSeverity(type: SecurityEventType): SecurityEvent['severity'] {
  const severityMap: Record<SecurityEventType, SecurityEvent['severity']> = {
    AUTH_SUCCESS: 'LOW',
    AUTH_FAILURE: 'MEDIUM',
    ADMIN_LOGIN: 'HIGH',
    ADMIN_LOGIN_FAILED: 'CRITICAL',
    FILE_UPLOAD: 'LOW',
    FILE_ACCESS: 'LOW',
    RATE_LIMIT_HIT: 'MEDIUM',
    CSRF_VIOLATION: 'HIGH',
    INVALID_UUID: 'MEDIUM',
    CRON_ACCESS: 'MEDIUM',
    SUSPICIOUS_ACTIVITY: 'HIGH',
    PASSWORD_CHANGE: 'MEDIUM',
    ACCOUNT_CREATION: 'LOW'
  }
  
  return severityMap[type] || 'MEDIUM'
}

/**
 * Logs a security event with structured data
 */
export function logSecurityEvent(
  type: SecurityEventType,
  options: {
    request?: NextRequest
    userId?: string
    userEmail?: string
    endpoint?: string
    details?: Record<string, any>
    customSeverity?: SecurityEvent['severity']
  } = {}
): void {
  const {
    request,
    userId,
    userEmail,
    endpoint,
    details = {},
    customSeverity
  } = options

  const event: SecurityEvent = {
    type,
    userId,
    userEmail,
    ipAddress: request ? getClientIP(request) : undefined,
    userAgent: request ? getUserAgent(request) : undefined,
    endpoint: endpoint || (request ? new URL(request.url).pathname : undefined),
    details,
    timestamp: new Date().toISOString(),
    severity: customSeverity || getSeverity(type)
  }

  // Log to console with structured format
  const logLevel = event.severity === 'CRITICAL' ? 'error' : 
                   event.severity === 'HIGH' ? 'warn' : 'info'
  
  console[logLevel](`[SECURITY:${event.severity}] ${event.type}`, {
    timestamp: event.timestamp,
    userId: event.userId,
    userEmail: event.userEmail,
    ipAddress: event.ipAddress,
    endpoint: event.endpoint,
    userAgent: event.userAgent,
    details: event.details
  })

  // In production, you could also send to external monitoring services:
  // - Sentry for error tracking
  // - LogRocket for session replay
  // - DataDog for metrics
  // - Custom webhook for alerts
  
  if (process.env.NODE_ENV === 'production' && event.severity === 'CRITICAL') {
    // Example: Send critical events to external service
    // await sendToMonitoringService(event)
    console.error('🚨 CRITICAL SECURITY EVENT - IMMEDIATE ATTENTION REQUIRED', event)
  }
}

/**
 * Logs authentication events
 */
export function logAuthEvent(
  success: boolean,
  request: NextRequest,
  userId?: string,
  userEmail?: string,
  details?: Record<string, any>
): void {
  logSecurityEvent(
    success ? 'AUTH_SUCCESS' : 'AUTH_FAILURE',
    {
      request,
      userId,
      userEmail,
      details: {
        ...details,
        authMethod: 'supabase'
      }
    }
  )
}

/**
 * Logs admin authentication events
 */
export function logAdminAuthEvent(
  success: boolean,
  request: NextRequest,
  details?: Record<string, any>
): void {
  logSecurityEvent(
    success ? 'ADMIN_LOGIN' : 'ADMIN_LOGIN_FAILED',
    {
      request,
      details: {
        ...details,
        authType: 'admin_password'
      }
    }
  )
}

/**
 * Logs file operation events
 */
export function logFileEvent(
  operation: 'upload' | 'access',
  request: NextRequest,
  userId: string,
  fileKey: string,
  details?: Record<string, any>
): void {
  logSecurityEvent(
    operation === 'upload' ? 'FILE_UPLOAD' : 'FILE_ACCESS',
    {
      request,
      userId,
      details: {
        ...details,
        fileKey,
        operation
      }
    }
  )
}

/**
 * Logs rate limiting events
 */
export function logRateLimitEvent(
  request: NextRequest,
  limitType: string,
  details?: Record<string, any>
): void {
  logSecurityEvent('RATE_LIMIT_HIT', {
    request,
    details: {
      ...details,
      limitType
    }
  })
}

/**
 * Logs CSRF violation events
 */
export function logCSRFViolation(
  request: NextRequest,
  details?: Record<string, any>
): void {
  logSecurityEvent('CSRF_VIOLATION', {
    request,
    details,
    customSeverity: 'HIGH'
  })
}

/**
 * Logs suspicious activity
 */
export function logSuspiciousActivity(
  request: NextRequest,
  reason: string,
  details?: Record<string, any>
): void {
  logSecurityEvent('SUSPICIOUS_ACTIVITY', {
    request,
    details: {
      ...details,
      reason
    },
    customSeverity: 'HIGH'
  })
}

/**
 * Logs cron endpoint access
 */
export function logCronAccess(
  request: NextRequest,
  endpoint: string,
  success: boolean,
  details?: Record<string, any>
): void {
  logSecurityEvent('CRON_ACCESS', {
    request,
    endpoint,
    details: {
      ...details,
      success
    }
  })
}
