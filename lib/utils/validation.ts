/**
 * Security validation utilities
 * Prevents SQL injection and validates input parameters
 */

// UUID v4 regex pattern
const UUID_V4_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

/**
 * Validates if a string is a valid UUID v4
 * @param uuid - String to validate
 * @returns boolean - True if valid UUID v4
 */
export function isValidUUID(uuid: string): boolean {
  if (typeof uuid !== 'string') {
    return false
  }
  
  return UUID_V4_REGEX.test(uuid)
}

/**
 * Validates and sanitizes a UUID parameter
 * Throws error if invalid to prevent SQL injection
 * @param uuid - UUID string to validate
 * @param paramName - Name of parameter for error messages
 * @returns string - Validated UUID
 * @throws Error if invalid UUID
 */
export function validateUUID(uuid: string, paramName: string = 'id'): string {
  if (!uuid) {
    throw new Error(`${paramName} is required`)
  }
  
  if (typeof uuid !== 'string') {
    throw new Error(`${paramName} must be a string`)
  }
  
  // Trim whitespace and convert to lowercase
  const cleanUuid = uuid.trim().toLowerCase()
  
  if (!isValidUUID(cleanUuid)) {
    throw new Error(`Invalid ${paramName} format. Must be a valid UUID.`)
  }
  
  return cleanUuid
}

/**
 * Validates multiple UUID parameters
 * @param uuids - Object with UUID parameters to validate
 * @returns Object with validated UUIDs
 * @throws Error if any UUID is invalid
 */
export function validateUUIDs(uuids: Record<string, string>): Record<string, string> {
  const validated: Record<string, string> = {}
  
  for (const [key, value] of Object.entries(uuids)) {
    validated[key] = validateUUID(value, key)
  }
  
  return validated
}

/**
 * Validates optional UUID (can be null/undefined)
 * @param uuid - UUID string to validate (optional)
 * @param paramName - Name of parameter for error messages
 * @returns string | null - Validated UUID or null
 * @throws Error if provided but invalid UUID
 */
export function validateOptionalUUID(uuid: string | null | undefined, paramName: string = 'id'): string | null {
  if (!uuid) {
    return null
  }
  
  return validateUUID(uuid, paramName)
}

/**
 * Validates integer parameters to prevent injection
 * @param value - Value to validate
 * @param paramName - Name of parameter for error messages
 * @param min - Minimum allowed value (optional)
 * @param max - Maximum allowed value (optional)
 * @returns number - Validated integer
 * @throws Error if invalid
 */
export function validateInteger(
  value: string | number, 
  paramName: string = 'value',
  min?: number,
  max?: number
): number {
  let num: number
  
  if (typeof value === 'string') {
    // Check if string contains only digits (and optional minus sign)
    if (!/^-?\d+$/.test(value.trim())) {
      throw new Error(`${paramName} must be a valid integer`)
    }
    num = parseInt(value.trim(), 10)
  } else if (typeof value === 'number') {
    if (!Number.isInteger(value)) {
      throw new Error(`${paramName} must be an integer`)
    }
    num = value
  } else {
    throw new Error(`${paramName} must be a number`)
  }
  
  if (isNaN(num)) {
    throw new Error(`${paramName} must be a valid number`)
  }
  
  if (min !== undefined && num < min) {
    throw new Error(`${paramName} must be at least ${min}`)
  }
  
  if (max !== undefined && num > max) {
    throw new Error(`${paramName} must be at most ${max}`)
  }
  
  return num
}

/**
 * Validates string parameters to prevent injection
 * @param value - Value to validate
 * @param paramName - Name of parameter for error messages
 * @param maxLength - Maximum allowed length (optional)
 * @param allowEmpty - Whether empty strings are allowed (default: false)
 * @returns string - Validated string
 * @throws Error if invalid
 */
export function validateString(
  value: string,
  paramName: string = 'value',
  maxLength?: number,
  allowEmpty: boolean = false
): string {
  if (typeof value !== 'string') {
    throw new Error(`${paramName} must be a string`)
  }
  
  const trimmed = value.trim()
  
  if (!allowEmpty && trimmed.length === 0) {
    throw new Error(`${paramName} cannot be empty`)
  }
  
  if (maxLength !== undefined && trimmed.length > maxLength) {
    throw new Error(`${paramName} must be ${maxLength} characters or less`)
  }
  
  return trimmed
}

/**
 * Validates email format
 * @param email - Email to validate
 * @returns string - Validated email
 * @throws Error if invalid email format
 */
export function validateEmail(email: string): string {
  const trimmed = validateString(email, 'email', 254) // RFC 5321 limit
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(trimmed)) {
    throw new Error('Invalid email format')
  }
  
  return trimmed.toLowerCase()
}

/**
 * Validates URL format
 * @param url - URL to validate
 * @param paramName - Name of parameter for error messages
 * @returns string - Validated URL
 * @throws Error if invalid URL format
 */
export function validateURL(url: string, paramName: string = 'url'): string {
  const trimmed = validateString(url, paramName)
  
  try {
    new URL(trimmed)
    return trimmed
  } catch {
    throw new Error(`${paramName} must be a valid URL`)
  }
}

/**
 * Sanitizes text content to prevent XSS
 * @param text - Text to sanitize
 * @param maxLength - Maximum allowed length
 * @returns string - Sanitized text
 */
export function sanitizeText(text: string, maxLength?: number): string {
  if (typeof text !== 'string') {
    return ''
  }
  
  let sanitized = text
    .trim()
    // Remove potential script tags
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove other potentially dangerous tags
    .replace(/<(iframe|object|embed|form|input|button)[^>]*>/gi, '')
    // Remove javascript: protocols
    .replace(/javascript:/gi, '')
    // Remove on* event handlers
    .replace(/\son\w+\s*=/gi, '')
  
  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength)
  }
  
  return sanitized
}

/**
 * Creates a validation error response
 * @param message - Error message
 * @param status - HTTP status code (default: 400)
 * @returns NextResponse with error
 */
export function createValidationError(message: string, status: number = 400) {
  return new Response(
    JSON.stringify({ error: message }),
    { 
      status,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}
