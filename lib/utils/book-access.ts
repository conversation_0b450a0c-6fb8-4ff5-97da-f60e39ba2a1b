import { SupabaseClient } from '@supabase/supabase-js'

export interface BookAccessResult {
  hasAccess: boolean
  accessType: 'free' | 'purchased' | 'library' | 'admin' | 'none'
  book?: {
    id: string
    title: string
    price_amount: number
    user_id: string
  }
}

/**
 * Check if a user has access to a book through multiple pathways:
 * 1. Book is free
 * 2. User has purchased the book
 * 3. Book is in user's library
 *
 * @param bookIdentifier - Can be either a book slug or UUID
 */
export async function checkBookAccess(
  supabase: SupabaseClient,
  userId: string,
  bookIdentifier: string
): Promise<BookAccessResult> {
  try {
    console.log('checkBookAccess: Checking access for user:', userId, 'book:', bookIdentifier)

    // First, try to find the book by slug, then by ID
    let book = null
    let bookError = null

    // Try by slug first
    const { data: bookBySlug, error: slugError } = await supabase
      .from('projects')
      .select('id, title, price_amount, user_id')
      .eq('slug', bookIdentifier)
      .eq('is_ebook', true)
      .single()

    if (bookBySlug) {
      book = bookBySlug
    } else {
      // Try by ID if slug doesn't work
      const { data: bookById, error: idError } = await supabase
        .from('projects')
        .select('id, title, price_amount, user_id')
        .eq('id', bookIdentifier)
        .eq('is_ebook', true)
        .single()

      book = bookById
      bookError = idError
    }

    console.log('checkBookAccess: Book query result:', { book, bookError })

    if (bookError || !book) {
      console.log('checkBookAccess: Book not found or error')
      return { hasAccess: false, accessType: 'none' }
    }

    // Use the actual book ID for subsequent queries
    const bookId = book.id

    // Check if user is admin - admins get access to all books
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    if (userProfile?.role === 'admin') {
      console.log('checkBookAccess: User is admin, granting access')
      return {
        hasAccess: true,
        accessType: 'admin', // Admins get access to all books
        book
      }
    }

    // Check if user is the author of the book
    if (book.user_id === userId) {
      console.log('checkBookAccess: User is the author, granting access')
      return {
        hasAccess: true,
        accessType: 'free', // Authors get free access to their own books
        book
      }
    }

    // For free books, still check if user has "purchased" (downloaded) it
    // This ensures the sales tracking works properly

    // Check if user has purchased this book
    console.log('checkBookAccess: Checking purchase status')
    const { data: purchaseData, error: purchaseError } = await supabase
      .from('book_purchases')
      .select('id')
      .eq('user_id', userId)
      .eq('project_id', bookId)
      .single()

    console.log('checkBookAccess: Purchase query result:', { purchaseData, purchaseError })

    if (purchaseData) {
      console.log('checkBookAccess: User has purchased book')
      return {
        hasAccess: true,
        accessType: 'purchased',
        book
      }
    }

    // Check if book is in user's library
    console.log('checkBookAccess: Checking library status')
    const { data: libraryData, error: libraryError } = await supabase
      .from('user_library')
      .select('access_type')
      .eq('user_id', userId)
      .eq('project_id', bookId)
      .single()

    console.log('checkBookAccess: Library query result:', { libraryData, libraryError })

    if (libraryData) {
      console.log('checkBookAccess: Book found in user library')
      return {
        hasAccess: true,
        accessType: 'library',
        book
      }
    }

    // No access found
    console.log('checkBookAccess: No access found')
    return {
      hasAccess: false,
      accessType: 'none',
      book
    }

  } catch (error) {
    console.error('Error checking book access:', error)
    return { hasAccess: false, accessType: 'none' }
  }
}

/**
 * Check if a user has access to read a book (stricter check for the read page)
 * @param bookIdentifier - Can be either a book slug or UUID
 */
export async function checkBookReadAccess(
  supabase: SupabaseClient,
  userId: string,
  bookIdentifier: string
): Promise<BookAccessResult> {
  const result = await checkBookAccess(supabase, userId, bookIdentifier)

  // For reading, we need either free access, purchase, library entry, or admin access
  if (result.hasAccess && ['free', 'purchased', 'library', 'admin'].includes(result.accessType)) {
    return result
  }

  return { hasAccess: false, accessType: 'none', book: result.book }
}
