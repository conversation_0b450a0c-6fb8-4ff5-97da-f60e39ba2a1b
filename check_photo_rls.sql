-- Check RLS policies for photos table
-- Run this to diagnose photo upload issues

-- 1. Check if <PERSON><PERSON> is enabled on photos table
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'photos';

-- 2. List all RLS policies on photos table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'photos'
ORDER BY policyname;

-- 3. Check current user's authentication
SELECT 
    auth.uid() as current_user_id,
    auth.role() as current_role;

-- 4. Test photo insertion permissions (replace with actual entry ID)
-- First, find a diary entry ID for testing
SELECT id, title, user_id 
FROM diary_entries 
WHERE user_id = auth.uid()
ORDER BY created_at DESC 
LIMIT 5;

-- 5. Check if user can read their own photos
SELECT 
    p.id,
    p.url,
    p.alt_text,
    p.moderation_status,
    p.diary_entry_id,
    de.user_id as entry_owner,
    de.title as entry_title
FROM photos p
JOIN diary_entries de ON de.id = p.diary_entry_id
WHERE de.user_id = auth.uid()
ORDER BY p.created_at DESC
LIMIT 10;

-- 6. Check if there are any photos that exist but aren't visible due to RLS
-- (This requires admin privileges)
-- SELECT COUNT(*) as total_photos_in_db FROM photos;
-- SELECT COUNT(*) as visible_photos FROM photos WHERE true;

-- 7. Test specific RLS policy conditions
-- Check if the diary entry exists and belongs to current user
SELECT 
    de.id,
    de.title,
    de.user_id,
    de.user_id = auth.uid() as user_owns_entry
FROM diary_entries de
WHERE de.user_id = auth.uid()
ORDER BY de.created_at DESC
LIMIT 5;

-- 8. Check for any photos in pending status
SELECT 
    p.id,
    p.moderation_status,
    p.created_at,
    de.title
FROM photos p
JOIN diary_entries de ON de.id = p.diary_entry_id
WHERE de.user_id = auth.uid()
AND p.moderation_status != 'approved'
ORDER BY p.created_at DESC;

-- 9. Check storage bucket permissions (if using Supabase storage)
-- This would need to be run in the Supabase dashboard
-- SELECT * FROM storage.buckets WHERE name = 'photos';

-- 10. Test inserting a photo (replace diary_entry_id with actual ID)
/*
INSERT INTO photos (
    diary_entry_id,
    url,
    alt_text,
    moderation_status
) VALUES (
    'your-diary-entry-id-here',
    'https://test-url.com/test.jpg',
    'Test photo',
    'approved'
) RETURNING *;
*/
