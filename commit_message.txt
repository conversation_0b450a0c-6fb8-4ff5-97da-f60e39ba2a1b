git add . && git commit -m "Complete unified role system implementation - remove all legacy role checks

## Comprehensive Role System Overhaul
- Remove ALL outdated role='writer'/'subscriber'/'reader' checks across codebase
- Update all authentication flows to use unified 'user' role system
- Fix mailing list, subscription, and follow flows to work with unified roles
- Update TypeScript type definitions to include 'user' role

## Files Updated - API Routes
- app/api/mailing-list/subscribe/route.ts - Remove writer role requirement
- app/api/create-test-subscriptions/route.ts - Update role filter
- app/api/withdraw/route.ts - Allow all users to request withdrawals
- app/api/profile/pricing/route.ts - Allow all users to set prices
- app/api/purchase-credits/route.ts - Allow all users to receive credits

## Files Updated - Pages & Components
- app/setup-subscriptions/page.tsx - Allow all users to be followed
- app/projects/[id]/page.tsx - Accept unified user roles
- app/write/projects/[id]/chapters/[chapterId]/page.tsx - Accept unified user roles
- app/feed/page.tsx - Show content from all users
- app/admin/page.tsx - Query all users as potential creators
- app/sitemap.ts - Include all users in sitemap generation
- app/u/[id]/mailing-list/page.tsx - Allow all users to have mailing lists
- app/[customUrl]/mailing-list/page.tsx - Allow all users to have mailing lists

## Files Updated - Type Definitions
- lib/supabase/client.ts - Add 'user' to all role type definitions
- Updated Row, Insert, Update, and Enum types to include 'user' role

## Issue Resolution
- Fixes 'must be a writer' error when trying to follow ChangeLog user
- Ensures all users can participate in subscription/follow system
- Removes artificial role barriers throughout the application
- Maintains security while enabling unified user experience
- Aligns entire codebase with unified authentication philosophy

## Technical Details
- Changed ALL role checks from 'writer' to ['user', 'admin']
- Updated TypeScript types to prevent future role-related errors
- Removed role-based restrictions that conflicted with unified system
- Preserved admin-only functionality where appropriate
- All users now have equal access to creator features

This completely resolves authentication issues and implements true unified roles."
