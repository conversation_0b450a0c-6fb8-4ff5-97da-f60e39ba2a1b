-- Fix sales count display issue that occurred after rating system fix
-- This ensures sales counts are properly calculated and displayed

-- First, let's check the current state
SELECT 
    'Current sales count status' as check_type,
    p.id,
    p.title,
    p.sales_count as current_sales_count,
    COUNT(bp.id) as actual_purchases,
    CASE
        WHEN p.sales_count = COUNT(bp.id) THEN 'MATCH ✓'
        ELSE 'MISMATCH ✗'
    END as status
FROM projects p
LEFT JOIN book_purchases bp ON p.id = bp.project_id
WHERE p.is_ebook = true
    AND p.is_complete = true
GROUP BY p.id, p.title, p.sales_count
HAVING p.sales_count > 0 OR COUNT(bp.id) > 0
ORDER BY p.sales_count DESC;

-- Fix the sales count trigger function to ensure it works correctly
CREATE OR REPLACE FUNCTION update_project_sales()
RETURNS TRIGGER 
SET search_path = ''
AS $$
BEGIN
    -- Update sales count based on actual book_purchases
    UPDATE public.projects 
    SET sales_count = (
        SELECT COUNT(*) 
        FROM public.book_purchases 
        WHERE project_id = COALESCE(NEW.project_id, OLD.project_id)
    )
    WHERE id = COALESCE(NEW.project_id, OLD.project_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger is properly set up
DROP TRIGGER IF EXISTS update_project_sales_trigger ON book_purchases;
CREATE TRIGGER update_project_sales_trigger
    AFTER INSERT OR UPDATE OR DELETE ON book_purchases
    FOR EACH ROW EXECUTE FUNCTION update_project_sales();

-- Fix any existing sales count mismatches
UPDATE projects 
SET sales_count = (
    SELECT COUNT(*) 
    FROM book_purchases 
    WHERE book_purchases.project_id = projects.id
)
WHERE is_ebook = true;

-- Verify the fix worked
SELECT 
    'After fix status' as check_type,
    p.id,
    p.title,
    p.sales_count as updated_sales_count,
    COUNT(bp.id) as actual_purchases,
    CASE
        WHEN p.sales_count = COUNT(bp.id) THEN 'MATCH ✓'
        ELSE 'MISMATCH ✗'
    END as status
FROM projects p
LEFT JOIN book_purchases bp ON p.id = bp.project_id
WHERE p.is_ebook = true
    AND p.is_complete = true
GROUP BY p.id, p.title, p.sales_count
HAVING p.sales_count > 0 OR COUNT(bp.id) > 0
ORDER BY p.sales_count DESC;

SELECT 'Sales count display issue fixed successfully!' as status;
